// 获取全局应用实例
const app = getApp()

// 全局变量：存储蓝牙接收的数据
var bleData = '';         // 存储眼科检测数据
var bleReposeData = '';   // 存储响应数据

/**
 * 在数组中查找指定键值对的索引
 * @param {Array} arr - 要搜索的数组
 * @param {string} key - 对象的键名
 * @param {*} val - 要匹配的值
 * @returns {number} 找到返回索引，未找到返回-1
 */
function inArray(arr, key, val) {
    for (let i = 0; i < arr.length; i++) {
        if (arr[i][key] === val) {
            return i;
        }
    }
    return -1;
}

/**
 * 将ArrayBuffer转换为MAC地址格式的字符串
 * @param {ArrayBuffer} buffer - 要转换的缓冲区
 * @returns {string} MAC地址格式的字符串 (xx:xx:xx:xx:xx:xx)
 */
function array2String(buffer) {
    let hexArr = Array.prototype.map.call(
        new Uint8Array(buffer),
        function (bit) {
            return ('00' + bit.toString(16)).slice(-2)
        }
    )
    return `${hexArr[7]}:${hexArr[6]}:${hexArr[5]}:${hexArr[2]}:${hexArr[1]}:${hexArr[0]}`
}

/**
 * ArrayBuffer转16进制字符串
 * @param {ArrayBuffer} buffer - 要转换的缓冲区
 * @returns {string} 16进制字符串
 */
function ab2hex(buffer) {
    var hexArr = Array.prototype.map.call(
        new Uint8Array(buffer),
        function (bit) {
            return ('00' + bit.toString(16)).slice(-2)
        }
    )
    return hexArr.join('');
}

Page({
    data: {
        // 蓝牙通信状态
        response: false,        // 是否等待响应
        udpport: '',           // UDP端口（暂未使用）
        devices: [],           // 扫描到的蓝牙设备列表
        connected: false,      // 蓝牙连接状态
        chs: [],              // 蓝牙特征值列表

        // 发送数据配置
        sendDataTxt: 1,       // 发送数据类型标识
        sendDataValue: '{"Name":"","Name.Base64":"546L5a2m5a2m","BirthDate":"1949-09-21","Sex":0,"CaseId":"123","DeviceStart":true,"Response":true}',
        deviceCyl: true,      // 设备柱镜开关

        // 眼科检测数据 - 右眼
        rPupil: '',           // 右眼瞳孔直径
        rGazeV: '',           // 右眼垂直注视方向
        rGazeH: '',           // 右眼水平注视方向
        rSE1: '',             // 右眼球镜度数1
        rDS1: '',             // 右眼柱镜度数1
        rDC1: '',             // 右眼柱镜轴位1
        rAxis1: '',           // 右眼轴位1
        rSE2: '',             // 右眼球镜度数2
        rDS2: '',             // 右眼柱镜度数2
        rDC2: '',             // 右眼柱镜轴位2
        rAxis2: '',           // 右眼轴位2

        // 眼科检测数据 - 左眼
        lPupil: '',           // 左眼瞳孔直径
        lGazeV: '',           // 左眼垂直注视方向
        lGazeH: '',           // 左眼水平注视方向
        lSE1: '',             // 左眼球镜度数1
        lDS1: '',             // 左眼柱镜度数1
        lDC1: '',             // 左眼柱镜轴位1
        lAxis1: '',           // 左眼轴位1
        lSE2: '',             // 左眼球镜度数2
        lDS2: '',             // 左眼柱镜度数2
        lDC2: '',             // 左眼柱镜轴位2
        lAxis2: '',           // 左眼轴位2

        // 双眼数据
        pd: '',               // 瞳距
    },

    /**
     * 页面加载时的生命周期函数
     */
    onLoad() {
        // 页面初始化逻辑
    },

    /**
     * 打开蓝牙适配器
     */
    openBluetoothAdapter() {
        wx.openBluetoothAdapter({
            success: (res) => {
                console.log('openBluetoothAdapter success', res)
                this.setData({
                    devices: []
                })
                this.startBluetoothDevicesDiscovery()
            },
            fail: (res) => {
                // 蓝牙未打开时的处理
                if (res.errCode === 10001) {
                    wx.onBluetoothAdapterStateChange(function (res) {
                        console.log('onBluetoothAdapterStateChange', res)
                        if (res.available) {
                            this.startBluetoothDevicesDiscovery()
                        }
                    })
                }
            }
        })
    },

    /**
     * 获取蓝牙适配器状态
     */
    getBluetoothAdapterState() {
        wx.getBluetoothAdapterState({
            success: (res) => {
                console.log('getBluetoothAdapterState', res)
                if (res.discovering) {
                    // 正在搜索设备
                    this.onBluetoothDeviceFound()
                } else if (res.available) {
                    // 蓝牙可用，开始搜索
                    this.startBluetoothDevicesDiscovery()
                }
            }
        })
    },

    /**
     * 开始搜索蓝牙设备
     */
    startBluetoothDevicesDiscovery() {
        if (this._discoveryStarted) {
            return
        }
        this._discoveryStarted = true
        wx.startBluetoothDevicesDiscovery({
            allowDuplicatesKey: true,
            success: (res) => {
                console.log('startBluetoothDevicesDiscovery success', res)
                this.onBluetoothDeviceFound()
            },
        })
    },

    /**
     * 停止搜索蓝牙设备
     */
    stopBluetoothDevicesDiscovery() {
        wx.stopBluetoothDevicesDiscovery()
    },
    /**
     * 监听搜索到新设备的事件
     */
    onBluetoothDeviceFound() {
        wx.onBluetoothDeviceFound((res) => {
            res.devices.forEach(device => {
                // 过滤掉没有名称的设备
                if (!device.name && !device.localName) {
                    return
                }
                const foundDevices = this.data.devices
                console.log(foundDevices);
                const idx = inArray(foundDevices, 'deviceId', device.deviceId)
                const data = {}
                if (idx === -1) {
                    // 新设备，添加到列表末尾
                    data[`devices[${foundDevices.length}]`] = device
                } else {
                    // 已存在设备，更新设备信息
                    data[`devices[${idx}]`] = device
                }
                this.setData(data)
            })
        })
    },

    /**
     * 创建蓝牙低功耗连接
     * @param {Event} e - 点击事件对象
     */
    createBLEConnection(e) {
        const ds = e.currentTarget.dataset
        const deviceId = ds.deviceId
        const name = ds.name
        wx.createBLEConnection({
            deviceId,
            success: (res) => {
                this.setData({
                    connected: true,
                    name,
                    deviceId,
                })
                // 连接成功后获取设备服务
                this.getBLEDeviceServices(deviceId)
            }
        })
        // 停止搜索设备
        this.stopBluetoothDevicesDiscovery()
    },

    /**
     * 关闭蓝牙连接
     */
    closeBLEConnection() {
        wx.closeBLEConnection({
            deviceId: this.data.deviceId
        })
        this.setData({
            connected: false,
            chs: [],
            canWrite: false,
        })
    },
    /**
     * 获取蓝牙设备的所有服务
     * @param {string} deviceId - 设备ID
     */
    getBLEDeviceServices(deviceId) {
        wx.getBLEDeviceServices({
            deviceId,
            success: (res) => {
                // 遍历服务，找到主服务
                for (let i = 0; i < res.services.length; i++) {
                    if (res.services[i].isPrimary) {
                        this.getBLEDeviceCharacteristics(deviceId, res.services[i].uuid)
                        return
                    }
                }
            }
        })
    },

    /**
     * 获取蓝牙设备服务的所有特征值
     * @param {string} deviceId - 设备ID
     * @param {string} serviceId - 服务ID
     */
    getBLEDeviceCharacteristics(deviceId, serviceId) {
        wx.getBLEDeviceCharacteristics({
            deviceId,
            serviceId,
            success: (res) => {
                console.log('getBLEDeviceCharacteristics success', res.characteristics)
                for (let i = 0; i < res.characteristics.length; i++) {
                    let item = res.characteristics[i]

                    // 如果特征值支持读取
                    if (item.properties.read) {
                        wx.readBLECharacteristicValue({
                            deviceId,
                            serviceId,
                            characteristicId: item.uuid,
                        })
                    }

                    // 如果特征值支持写入
                    if (item.properties.write) {
                        this.setData({
                            canWrite: true
                        })
                        // 保存写入相关的ID
                        this._deviceId = deviceId
                        this._serviceId = serviceId
                        this._characteristicId = item.uuid
                        this.writeBLECharacteristicValue()
                    }

                    // 如果特征值支持通知或指示
                    if (item.properties.notify || item.properties.indicate) {
                        wx.notifyBLECharacteristicValueChange({
                            deviceId,
                            serviceId,
                            characteristicId: item.uuid,
                            state: true,
                        })
                    }
                }
            },
            fail(res) {
                console.error('getBLEDeviceCharacteristics', res)
            }
        })
        // 操作之前先监听，保证第一时间获取数据
        wx.onBLECharacteristicValueChange((characteristic) => {
            // 注：idx和data变量在此处未使用，可能是历史遗留代码
            const idx = inArray(this.data.chs, 'uuid', characteristic.characteristicId)
            const data = {}

            // 根据是否等待响应来处理不同类型的数据
            if (this.data.response) {
                // 处理响应数据（通常是设备返回的确认信息）
                if (this.bleReposeData == undefined || this.bleReposeData == null || this.bleReposeData === '') {
                    this.bleReposeData = this.hexToString(ab2hex(characteristic.value))
                } else {
                    this.bleReposeData += this.hexToString(ab2hex(characteristic.value))
                }

                // 检查是否接收到完整的JSON响应
                if (this.bleReposeData.indexOf("{") !== -1 && this.bleReposeData.indexOf("}") !== -1) {
                    var jsonStr = this.bleReposeData
                    jsonStr = jsonStr.replace(" ", "")
                    if (typeof jsonStr != 'object') {
                        jsonStr = jsonStr.replace(/\ufeff/g, "") // 移除BOM字符
                    }
                    var jsonObj = JSON.parse(jsonStr)

                    // 如果响应包含消息，显示给用户
                    if (this.bleReposeData.indexOf("Msg") >= 0) {
                        wx.showToast({
                            title: 'Msg：' + jsonObj.Msg,
                            icon: 'success',
                            duration: 2000
                        })
                    }

                    // 重置响应状态
                    this.setData({
                        "response": false
                    })
                    this.bleReposeData = '';
                }
            } else {
                // 处理眼科检测数据
                if (this.bleData == undefined || this.bleData == null || this.bleData === '') {
                    this.bleData = this.hexToString(ab2hex(characteristic.value))
                } else {
                    this.bleData += this.hexToString(ab2hex(characteristic.value))
                }
                // 检查数据是否接收完成
                this.BleDataCheck()
            }
        })
    },
    /**
     * 校验蓝牙数据是否接收完成并解析数据
     * 支持三种数据格式：
     * 1. SW...FE格式的固定位置数据（右眼/左眼分别传输）
     * 2. SW{JSON}FE格式的混合数据
     * 3. 纯JSON格式数据
     */
    BleDataCheck() {
        // 检查是否为SW...FE格式的完整数据包
        if (this.bleData.indexOf("SW") !== -1 && this.bleData.indexOf("FE") !== -1) {
            console.log("SW_____FE")
            console.log(this.bleData)

            // 获取眼别标识（位置21）：1=右眼，2=左眼，其他=JSON格式
            var eyeType = this.bleData.substring(21, 22)
            console.log(eyeType)

            if (eyeType == '1') {
                // 右眼数据解析（固定位置格式）
                this.setData({
                    rPupil: this.bleData.substring(40, 43),    // 右眼瞳孔直径
                    rSE1: this.bleData.substring(43, 48),      // 右眼球镜度数1
                    rDS1: this.bleData.substring(48, 53),      // 右眼柱镜度数1
                    rDC1: this.bleData.substring(53, 58),      // 右眼柱镜轴位1
                    rAxis1: this.bleData.substring(58, 61),    // 右眼轴位1
                    rSE2: this.bleData.substring(61, 66),      // 右眼球镜度数2
                    rDS2: this.bleData.substring(66, 71),      // 右眼柱镜度数2
                    rDC2: this.bleData.substring(71, 76),      // 右眼柱镜轴位2
                    rAxis2: this.bleData.substring(76, 79),    // 右眼轴位2
                    pd: this.bleData.substring(79, 81),        // 瞳距
                    rGazeH: this.FormatGazeHV(this.bleData.substring(85, 88), false), // 右眼水平注视
                    rGazeV: this.FormatGazeHV(this.bleData.substring(88, 91)),        // 右眼垂直注视
                })
            } else if (eyeType == '2') {
                // 左眼数据解析（固定位置格式）
                this.setData({
                    lPupil: this.bleData.substring(40, 43),    // 左眼瞳孔直径
                    lSE1: this.bleData.substring(43, 48),      // 左眼球镜度数1
                    lDS1: this.bleData.substring(48, 53),      // 左眼柱镜度数1
                    lDC1: this.bleData.substring(53, 58),      // 左眼柱镜轴位1
                    lAxis1: this.bleData.substring(58, 61),    // 左眼轴位1
                    lSE2: this.bleData.substring(61, 66),      // 左眼球镜度数2
                    lDS2: this.bleData.substring(66, 71),      // 左眼柱镜度数2
                    lDC2: this.bleData.substring(71, 76),      // 左眼柱镜轴位2
                    lAxis2: this.bleData.substring(76, 79),    // 左眼轴位2
                    pd: this.bleData.substring(79, 81),        // 瞳距
                    lGazeH: this.FormatGazeHV(this.bleData.substring(85, 88), false), // 左眼水平注视
                    lGazeV: this.FormatGazeHV(this.bleData.substring(88, 91)),        // 左眼垂直注视
                })
            } else {
                // SW{JSON}FE格式：包含JSON数据的混合格式
                console.log("SWjson")
                var startIndex = this.bleData.indexOf("{")
                var endIndex = this.bleData.indexOf("FE")
                var jsonStr = this.bleData.substring(startIndex, endIndex)
                console.log(jsonStr)
                jsonStr = jsonStr.replace(" ", "")
                if (typeof jsonStr != 'object') {
                    jsonStr = jsonStr.replace(/\ufeff/g, ""); // 移除BOM字符
                }
                var jsonObj = JSON.parse(jsonStr)
                var clinicResult = jsonObj.ClinicResult[0]
                this.setData({
                    rPupil: clinicResult.RPupil,
                    lPupil: clinicResult.LPupil,
                    pd: clinicResult.PD,
                    rGazeV: this.FormatGazeHV(clinicResult.RGazeV),
                    lGazeV: this.FormatGazeHV(clinicResult.LGazeV),
                    rGazeH: this.FormatGazeHV(clinicResult.RGazeH, false),
                    lGazeH: this.FormatGazeHV(clinicResult.LGazeH, false),
                    rSE1: clinicResult.RSE1,
                    rDS1: clinicResult.RDS1,
                    rDC1: clinicResult.RDC1,
                    rAxis1: clinicResult.RAxis1,
                    rSE2: clinicResult.RSE2,
                    rDS2: clinicResult.RDS2,
                    rDC2: clinicResult.RDC2,
                    rAxis2: clinicResult.RAxis2,
                    lSE1: clinicResult.LSE1,
                    lDS1: clinicResult.LDS1,
                    lDC1: clinicResult.LDC1,
                    lAxis1: clinicResult.LAxis1,
                    lSE2: clinicResult.LSE2,
                    lDS2: clinicResult.LDS2,
                    lDC2: clinicResult.LDC2,
                    lAxis2: clinicResult.LAxis2,
                })
            }
            this.writeBLECharacteristicValue()
            this.bleData = ''
            return;
        } else if (this.bleData.indexOf("FE") === -1 && this.bleData.indexOf("{") !== -1 && this.bleData.indexOf("}") !== -1) {
            //纯Json    不包含FE
            console.log("json")
            var jsonStr = this.bleData
            jsonStr = jsonStr.replace(" ", "")
            if (typeof jsonStr != 'object') {
                jsonStr = jsonStr.replace(/\ufeff/g, ""); //重点
            }
            var jsonObj = JSON.parse(jsonStr)
            var clinicResult = jsonObj.ClinicResult[0]
            console.log(this.bleData)
            this.setData({
                rPupil: clinicResult.RPupil,
                lPupil: clinicResult.LPupil,
                pd: clinicResult.PD,
                rGazeV: this.FormatGazeHV(clinicResult.RGazeV),
                lGazeV: this.FormatGazeHV(clinicResult.LGazeV),
                rGazeH: this.FormatGazeHV(clinicResult.RGazeH, false),
                lGazeH: this.FormatGazeHV(clinicResult.LGazeH, false),
                rSE1: clinicResult.RSE1,
                rDS1: clinicResult.RDS1,
                rDC1: clinicResult.RDC1,
                rAxis1: clinicResult.RAxis1,
                rSE2: clinicResult.RSE2,
                rDS2: clinicResult.RDS2,
                rDC2: clinicResult.RDC2,
                rAxis2: clinicResult.RAxis2,
                lSE1: clinicResult.LSE1,
                lDS1: clinicResult.LDS1,
                lDC1: clinicResult.LDC1,
                lAxis1: clinicResult.LAxis1,
                lSE2: clinicResult.LSE2,
                lDS2: clinicResult.LDS2,
                lDC2: clinicResult.LDC2,
                lAxis2: clinicResult.LAxis2,
            })
            this.writeBLECharacteristicValue()
            this.bleData = ''
            return;
        } else {
            // 数据不完整，继续等待
        }
    },

    /**
     * 格式化注视方向数据，添加方向箭头
     * @param {string|number} str - 注视方向数值
     * @param {boolean} isGazeV - 是否为垂直方向，默认true
     * @returns {string} 格式化后的注视方向字符串
     */
    FormatGazeHV(str, isGazeV = true) {
        if (str === undefined || str == null || str === '' || str === 'undefined') return '';
        if (str == 0) return str;

        if (isGazeV) {
            // 垂直方向：负值向上，正值向下
            if (str < 0) {
                return "⬆" + Math.abs(str)
            } else {
                return "⬇" + Math.abs(str)
            }
        } else {
            // 水平方向：负值向右，正值向左
            if (str < 0) {
                return "➡" + Math.abs(str)
            } else {
                return "⬅" + Math.abs(str)
            }
        }
    },

    /**
     * 将16进制字符串转换为普通字符串
     * @param {string} str - 16进制字符串
     * @returns {string} 转换后的字符串
     */
    hexToString(str) {
        var val = "",
            len = str.length / 2;
        for (var i = 0; i < len; i++) {
            val += String.fromCharCode(parseInt(str.substr(i * 2, 2), 16));
        }
        return this.utf8to16(val);
    },

    /**
     * UTF-8转UTF-16编码，处理中文乱码问题
     * @param {string} str - 需要转换的字符串
     * @returns {string} 转换后的字符串
     */
    utf8to16(str) {
        var out, i, len, c;
        var char2, char3;
        out = "";
        len = str.length;
        i = 0;
        while (i < len) {
            c = str.charCodeAt(i++);
            switch (c >> 4) {
                case 0:
                case 1:
                case 2:
                case 3:
                case 4:
                case 5:
                case 6:
                case 7:
                    // 0xxxxxxx - ASCII字符
                    out += str.charAt(i - 1);
                    break;
                case 12:
                case 13:
                    // 110x xxxx   10xx xxxx - 双字节字符
                    char2 = str.charCodeAt(i++);
                    out += String.fromCharCode(((c & 0x1F) << 6) | (char2 & 0x3F));
                    break;
                case 14:
                    // 1110 xxxx  10xx xxxx  10xx xxxx - 三字节字符
                    char2 = str.charCodeAt(i++);
                    char3 = str.charCodeAt(i++);
                    out += String.fromCharCode(((c & 0x0F) << 12) |
                        ((char2 & 0x3F) << 6) |
                        ((char3 & 0x3F) << 0));
                    break;
            }
        }
        return out;
    },

    /**
     * 字符串转ArrayBuffer
     * @param {string} str - 要转换的字符串
     * @returns {ArrayBuffer} 转换后的ArrayBuffer
     */
    strToArrayBuffer(str) {
        var array = new Uint8Array(str.length);
        for (var i = 0; i < str.length; i++) {
            array[i] = str.charCodeAt(i);
        }
        return array.buffer
    },

    /**
     * 向蓝牙设备发送确认信息"SUCC"
     */
    writeBLECharacteristicValue() {
        wx.writeBLECharacteristicValue({
            deviceId: this._deviceId,
            serviceId: this._serviceId,
            characteristicId: this._characteristicId,
            value: this.strToArrayBuffer('SUCC'),
        })
    },

    /**
     * 关闭蓝牙适配器
     */
    closeBluetoothAdapter() {
        wx.closeBluetoothAdapter()
        this._discoveryStarted = false

        this.setData({
            connected: false,
            chs: [],
            canWrite: false,
        })
    },

    /**
     * 切换设备柱镜开关状态
     */
    deviceCylChange() {
        if (this.data.deviceCyl) {
            this.setData({
                deviceCyl: false
            })
        } else {
            this.setData({
                deviceCyl: true
            })
        }
    },

    /**
     * 发送数据内容变更事件处理
     * @param {Event} e - 输入事件对象
     */
    sendDataValueChange(e) {
        this.setData({
            sendDataValue: e.detail.value
        })
    },

    /**
     * 循环切换发送数据类型（1->2->3->1）
     */
    sendDataTxtChange() {
        if (this.data.sendDataTxt == 1) {
            this.setData({
                sendDataTxt: 2
            })
            return;
        }
        if (this.data.sendDataTxt == 2) {
            this.setData({
                sendDataTxt: 3
            })
            return;
        }
        if (this.data.sendDataTxt == 3) {
            this.setData({
                sendDataTxt: 1
            })
            return;
        }
    },
    /**
     * 清空所有眼科检测数据
     */
    clearData() {
        this.setData({
            response: false,
            // 右眼数据清空
            rPupil: '',
            rSE1: '',
            rDS1: '',
            rDC1: '',
            rAxis1: '',
            rSE2: '',
            rDS2: '',
            rDC2: '',
            rAxis2: '',
            rGazeH: '',
            rGazeV: '',
            // 左眼数据清空
            lPupil: '',
            lSE1: '',
            lDS1: '',
            lDC1: '',
            lAxis1: '',
            lSE2: '',
            lDS2: '',
            lDC2: '',
            lAxis2: '',
            lGazeH: '',
            lGazeV: '',
            // 双眼数据清空
            pd: '',
        })
        // 清空蓝牙数据缓存
        this.bleData = '';
        this.bleReposeData = '';
    },

    /**
     * 向蓝牙设备发送JSON数据
     * 根据数据中是否包含Response字段来设置响应状态
     */
    sendData() {
        var jsonStr = this.data.sendDataValue
        jsonStr = jsonStr.replace(" ", "")
        if (typeof jsonStr != 'object') {
            jsonStr = jsonStr.replace(/\ufeff/g, "") // 移除BOM字符
        }
        var jsonObj = JSON.parse(jsonStr)
        console.log(this.data.sendDataValue.indexOf("Response"))

        // 检查数据中是否包含Response字段
        if (this.data.sendDataValue.indexOf("Response") >= 0) {
            if (jsonObj.Response == true) {
                // 需要等待设备响应
                this.setData({
                    "response": true
                })
            } else {
                // 不需要等待设备响应
                this.setData({
                    "response": false
                })
            }
        } else {
            // 默认等待响应
            this.setData({
                "response": true
            })
        }

        // 发送JSON数据到蓝牙设备
        wx.writeBLECharacteristicValue({
            deviceId: this._deviceId,
            serviceId: this._serviceId,
            characteristicId: this._characteristicId,
            value: this.strToArrayBuffer(JSON.stringify(jsonObj)),
        })
    },

    /**
     * 微信小程序分享功能
     * @returns {Object} 分享配置对象
     */
    onShareAppMessage() {
        console.log('fenxiang')
        return {
            title: '眼科蓝牙检测设备',
            path: '/pages/index/index'
        }
    }
})