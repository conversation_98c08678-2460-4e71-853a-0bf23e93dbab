<template>
    <view class="bg-white">
        <view class="bg">
            <image class="bg-img" src="@/static/logo.png"></image>
        </view>
        <view class="form">
            <u--form labelPosition="left" :model="model1" :rules="rules" ref="uForm" :labelWidth="200">
                <u-form-item label="检查项目" prop="userInfo.checkTypeName" borderBottom @click="checkTypeShow = true;">
                    <u--input v-model="model1.userInfo.checkTypeName" readonly disabledColor="#ffffff"
                        placeholder="请选检查项目" border="none" clearable></u--input>
                    <u-icon slot="right" name="arrow-right"></u-icon>
                </u-form-item>
                <u-form-item label="学校" prop="userInfo.schoolName" borderBottom @click="openSchoolShow">
                    <u--input v-model="model1.userInfo.schoolName" readonly disabledColor="#ffffff" placeholder="请选择学校"
                        border="none" clearable></u--input>
                    <u-icon slot="right" name="arrow-right"></u-icon>
                </u-form-item>
                <u-form-item v-if="model1.userInfo.checkType >= 2" label="设备" prop="userInfo.deviceName" borderBottom
                    @click="openDeviceShow">
                    <u--input v-model="model1.userInfo.deviceName" readonly disabledColor="#ffffff" placeholder="请选择设备"
                        border="none" clearable></u--input>
                    <u-icon slot="right" name="arrow-right"></u-icon>
                </u-form-item>
                <view class="startCheck">
                    <u-button type="primary" size="small" text="扫描学生二维码开始检查" @click="startCheckCode"></u-button>
                    <view class="tip-container">
                        <view class="hint-text" @click="handleNoStudentTip">
                            没有学生？
                        </view>
                    </view>
                </view>
            </u--form>

            <u-action-sheet :show="checkTypeShow" @close="closeCheckType" title="请输入检查项目">
                <scroll-view scroll-y class="action-sheet-list">
                    <view v-for="(item, index) in checkTypeList" :key="index" class="action-sheet-item"
                        @tap="checkTypeSelect(item)" hover-class="item-hover">
                        {{ item.label }}
                    </view>

                    <view v-if="checkTypeList.length === 0" class="empty-tip">
                        暂无匹配数据
                    </view>
                </scroll-view>
            </u-action-sheet>

            <u-action-sheet :show="schoolShow" @close="closeSchool" title="请输入学校">
                <view class="search-box">
                    <u-search v-model="schoolKeyword" placeholder="请选择学校" :showAction="false" @change="onSchoolSearch"
                        :clearabled="true"></u-search>
                </view>
                <scroll-view scroll-y class="action-sheet-list">
                    <view v-for="(item, index) in filteredSchoolList" :key="index" class="action-sheet-item"
                        @tap="schoolSelect(item)" hover-class="item-hover">
                        {{ item.name }}
                    </view>

                    <view v-if="filteredSchoolList.length === 0" class="empty-tip">
                        暂无匹配数据
                    </view>
                </scroll-view>
            </u-action-sheet>

            <u-action-sheet :show="deviceShow" @close="closeDevice" title="请选择设备">
                <view class="search-box">
                    <u-search v-model="deviceKeyword" placeholder="请选择设备" :showAction="false" @change="onDeviceSearch"
                        :clearabled="true"></u-search>
                </view>
                <view class="device-actions">
                    <u-button type="primary" size="mini" @click="refreshDeviceList" :loading="bluetoothState.isScanning"
                        :disabled="bluetoothState.isScanning">
                        {{ bluetoothState.isScanning ? '扫描中...' : '刷新设备' }}
                    </u-button>
                    <text class="device-count">已发现 {{ filteredDeviceList.length }} 个设备</text>
                </view>
                <scroll-view scroll-y class="action-sheet-list">
                    <view v-for="(item, index) in filteredDeviceList" :key="index" class="device-item"
                        @tap="deviceSelect(item)" hover-class="item-hover">
                        <view class="device-info">
                            <view class="device-name">{{ item.name || '未知设备' }}</view>
                            <view class="device-details">
                                <text class="device-id">{{ item.deviceId }}</text>
                                <text v-if="item.rssi" class="signal-strength">信号: {{ item.rssi }}dBm</text>
                            </view>
                        </view>
                        <view class="device-status">
                            <text v-if="item.connectionStatus === 'connecting'" class="status-connecting">连接中...</text>
                            <text v-else-if="item.isConnected" class="status-connected">✓ 已连接</text>
                            <text v-else class="status-available">可连接</text>
                        </view>
                    </view>

                    <view v-if="filteredDeviceList.length === 0" class="empty-tip">
                        暂无匹配数据
                    </view>
                </scroll-view>
            </u-action-sheet>
        </view>
    </view>
</template>

<script>
import {
    getSchoolList,
} from '@/utils/api/school.js'
import {
    getStudentById,
} from '@/utils/api/student.js'

export default {
    components: {

    },
    data() {
        return {
            checkTypeShow: false,
            checkTypeList: [{
                label: '视力',
                id: '1'
            }, {
                label: '屈光(旧)',
                id: '2'
            }, {
                label: '屈光(新)',
                id: '3'
            }],
            isAndroid: false,
            model1: {
                userInfo: {
                    checkType: '',
                    checkTypeName: '',
                    schoolName: '',
                    schoolId: '',
                    deviceName: ''
                },
            },
            rules: {},
            schoolList: [],
            schoolShow: false,
            schoolKeyword: '',
            deviceList: [],
            deviceShow: false,
            deviceKeyword: '',
            jsonStr: '',

            // 优化后的蓝牙状态管理
            bluetoothState: {
                isScanning: false,
                isConnecting: false,
                isConnected: false,
                adapterInitialized: false,
                currentDevice: null,
                scanListener: null,
                connectionListener: null
            },

            // 蓝牙配置常量
            BLUETOOTH_CONFIG: {
                SCAN_TIMEOUT: 10000,
                CONNECTION_TIMEOUT: 15000,
                MAX_RETRY_COUNT: 3,
                HEARTBEAT_INTERVAL: 5000,
                UUID_PROFILES: {
                    STANDARD_SPP: {
                        service: '00001101-0000-1000-8000-00805F9B34FB',
                        writeChar: '00001101-0000-1000-8000-00805F9B34FB'
                    },
                    WCH_CH9140: {
                        service: '0000FFE0-0000-1000-8000-00805F9B34FB',
                        writeChar: '0000FFE1-0000-1000-8000-00805F9B34FB'
                    },
                    HC_05: {
                        service: '00001101-0000-1000-8000-00805F9B34FB',
                        writeChar: '00001102-0000-1000-8000-00805F9B34FB'
                    }
                }
            },

            // 设备连接参数
            _deviceParams: null,
            _retryCount: 0,
            _reconnectCount: 0,

            // 屈光2专用参数
            isResponseMode: false,

            // 扫描相关
            scanTimeoutId: null,
        }
    },
    beforeDestroy() {
        this.cleanupBluetoothResources();
    },

    onUnload() {
        this.cleanupBluetoothResources();
    },

    onHide() {
        // 页面隐藏时停止扫描但保持连接
        if (this.bluetoothState.isScanning) {
            this.stopDeviceDiscovery();
        }
    },
    computed: {
        filteredSchoolList() {
            if (!this.schoolKeyword) {
                return this.schoolList;
            }
            const keyword = this.schoolKeyword.toLowerCase();
            return this.schoolList.filter(item =>
                item.name.toLowerCase().includes(keyword)
            );
        },

        filteredDeviceList() {
            if (!this.deviceKeyword) {
                return this.deviceList;
            }
            const keyword = this.deviceKeyword.toLowerCase();
            return this.deviceList.filter(item =>
                item.name.toLowerCase().includes(keyword)
            );
        },
    },
    watch: {

    },
    onLoad() {
        // 初始化平台信息
        const systemInfo = uni.getSystemInfoSync();
        this.isAndroid = systemInfo.platform.toLowerCase() === 'android';

        // 设置连接状态监听
        this.setupConnectionStateListener();
    },
    onShow() {
        // 延迟检查蓝牙状态，避免页面刚加载时的误报
        setTimeout(() => {
            this.checkBluetoothAdapterStateIfNeeded();
        }, 500);
    },
    onUnload() {
        if (this.listener) {
            uni.offBluetoothDeviceFound(this.listener);
        }
    },
    methods: {

        /**
         * 智能检查蓝牙适配器状态（仅在需要时检查）
         */
        checkBluetoothAdapterStateIfNeeded() {
            // 只有在用户已经进行过蓝牙操作时才检查状态
            if (!this.bluetoothState.adapterInitialized) {
                return; // 避免页面初始化时的误报
            }

            uni.getBluetoothAdapterState({
                success: (res) => {
                    if (!res.available) {
                        this.showToast('蓝牙不可用，请检查设备');
                        this.bluetoothState.adapterInitialized = false;
                    }
                },
                fail: (err) => {
                    console.error('蓝牙状态获取失败:', err);
                    // 只有在明确初始化过的情况下才提示错误
                    if (this.bluetoothState.adapterInitialized) {
                        this.showToast('蓝牙状态获取失败');
                    }
                }
            });
        },

        /**
         * 检查蓝牙适配器状态（强制检查）
         */
        checkBluetoothAdapterState() {
            return new Promise((resolve, reject) => {
                uni.getBluetoothAdapterState({
                    success: (res) => {
                        if (!res.available) {
                            reject(new Error('蓝牙不可用，请检查设备'));
                        } else {
                            resolve(res);
                        }
                    },
                    fail: (err) => {
                        console.error('蓝牙状态获取失败:', err);
                        reject(new Error('蓝牙状态获取失败'));
                    }
                });
            });
        },
        closeCheckType() {
            this.checkTypeShow = false
        },
        closeSchool() {
            this.schoolKeyword = ''
            this.schoolShow = false
        },
        closeDevice() {
            this.deviceKeyword = ''
            this.deviceShow = false
        },
        checkTypeSelect(e) {
            this.model1.userInfo.checkTypeName = e.label
            this.model1.userInfo.checkType = e.id
            this.closeCheckType()
        },
        schoolSelect(e) {
            this.model1.userInfo.schoolName = e.name
            this.model1.userInfo.schoolId = e.id
            this.closeSchool()
        },
        async deviceSelect(e) {
            // 防止重复连接
            if (this.bluetoothState.isConnecting) {
                this.showToast('正在连接中，请稍候...');
                return;
            }

            // 根据检查项目类型选择连接方法
            if (this.model1.userInfo.checkType === '3') {
                // 屈光2使用index.js的连接方法
                this.connectDeviceForRefraction2(e);
            } else {
                // 屈光使用现有方法
                this.connectDeviceForRefraction(e);
            }
        },

        /**
         * 屈光连接方法（现有方法）
         */
        async connectDeviceForRefraction(e) {
            const _this = this;

            // 先更新UI状态为连接中
            this.bluetoothState.isConnecting = true;
            this.updateDeviceConnectionStatus(e.deviceId, 'connecting');

            // 增强UUID配置（兼容主流厂商）
            const UUID_PROFILES = {
                STANDARD_SPP: {
                    service: '00001101-0000-1000-8000-00805F9B34FB',
                    writeChar: '00001101-0000-1000-8000-00805F9B34FB'
                },
                WCH_CH9140: {
                    service: '0000FFE0-0000-1000-8000-00805F9B34FB',
                    writeChar: '0000FFE1-0000-1000-8000-00805F9B34FB'
                },
                HC_05: {
                    service: '00001101-0000-1000-8000-00805F9B34FB',
                    writeChar: '00001102-0000-1000-8000-00805F9B34FB'
                }
            };

            uni.showLoading({
                title: '连接中...',
                mask: true
            });

            // 超时控制
            const timeout = setTimeout(() => {
                uni.hideLoading();
                uni.$u.toast('连接超时');
                _this.handleConnectionFailure(e.deviceId, '连接超时');
            }, this.BLUETOOTH_CONFIG.CONNECTION_TIMEOUT);

            try {
                // 增强连接协议
                await _this._createConnection(e.deviceId);
                await new Promise(resolve => setTimeout(resolve, 1500));

                // 服务发现流程优化
                const services = await _this._getBLEServices(e.deviceId);
                let sppProfile = null;

                // 多协议服务匹配
                for (const profile of Object.values(UUID_PROFILES)) {
                    const service = services.find(s =>
                        s.uuid.toUpperCase() === profile.service.toUpperCase()
                    );
                    if (service) {
                        sppProfile = {
                            ...profile,
                            serviceId: service.uuid
                        };
                        break;
                    }
                }

                if (!sppProfile) throw new Error('未找到兼容的SPP服务');

                // 特征值发现优化
                const characteristics = await _this._getCharacteristics(
                    e.deviceId,
                    sppProfile.serviceId
                );

                const writeChar = characteristics.find(c =>
                    c.uuid.toUpperCase() === sppProfile.writeChar.toUpperCase() &&
                    (c.properties.write || c.properties.writeWithoutResponse)
                );

                if (!writeChar) throw new Error('写入特征不可用');

                // 设备参数增强
                _this._deviceParams = {
                    deviceId: e.deviceId,
                    name: e.name,
                    serviceId: sppProfile.serviceId,
                    writeCharId: writeChar.uuid,
                    characteristics: characteristics,
                    mtu: 20, // 初始默认值
                    protocol: sppProfile.type || 'UNKNOWN',
                    manufacturer: e.manufacturerData ?
                        this._parseManufacturerData(e.manufacturerData) : 'Unknown'
                };

                // Android MTU协商
                // #ifdef APP-PLUS
                const mtuRes = await new Promise(resolve => {
                    plus.bluetooth.setBLEMTU({
                        deviceId: e.deviceId,
                        mtu: 512,
                        success: resolve,
                        fail: () => resolve({
                            mtu: 23
                        })
                    });
                });
                _this._deviceParams.mtu = mtuRes.mtu;
                // #endif

                // 发送设备初始化指令
                await _this._sendInitSequence();

                // 连接成功处理
                clearTimeout(timeout);
                uni.hideLoading();
                uni.$u.toast(`${e.name}连接成功`);

                // 更新状态
                this.model1.userInfo.deviceName = e.name;
                this.bluetoothState.isConnecting = false;
                this.bluetoothState.isConnected = true;
                this.bluetoothState.currentDevice = _this._deviceParams;

                // 更新设备列表状态
                this.updateDeviceConnectionStatus(e.deviceId, 'connected');

                // 关闭设备选择弹窗
                this.closeDevice();

                // 启动数据通道
                _this._startNotification();

            } catch (error) {
                clearTimeout(timeout);
                uni.hideLoading();
                console.error('设备连接失败:', error);

                // 连接失败处理
                this.handleConnectionFailure(e.deviceId, error.message);
            }
        },

        /**
         * 处理连接失败
         */
        handleConnectionFailure(deviceId, errorMessage) {
            // 重置连接状态
            this.bluetoothState.isConnecting = false;
            this.bluetoothState.isConnected = false;
            this.bluetoothState.currentDevice = null;

            // 更新设备列表状态为未连接
            this.updateDeviceConnectionStatus(deviceId, 'disconnected');

            // 显示错误信息
            uni.$u.toast(`连接失败: ${errorMessage}`);

            // 重置设备参数
            this._resetConnection();
        },

        /**
         * 屈光2连接方法（参考index.js）
         */
        connectDeviceForRefraction2(e) {
            const _this = this;

            // 更新UI状态为连接中
            this.bluetoothState.isConnecting = true;
            this.updateDeviceConnectionStatus(e.deviceId, 'connecting');

            uni.showLoading({
                title: '连接中...',
                mask: true
            });

            // 使用微信小程序的蓝牙API（参考index.js）
            uni.createBLEConnection({
                deviceId: e.deviceId,
                success: (res) => {
                    console.log('createBLEConnection success', res);

                    // 连接成功，更新状态
                    _this.bluetoothState.isConnecting = false;
                    _this.bluetoothState.isConnected = true;
                    _this.bluetoothState.currentDevice = {
                        deviceId: e.deviceId,
                        name: e.name
                    };

                    // 保存设备信息
                    _this._deviceParams = {
                        deviceId: e.deviceId,
                        name: e.name
                    };

                    // 更新UI
                    _this.model1.userInfo.deviceName = e.name;
                    _this.updateDeviceConnectionStatus(e.deviceId, 'connected');

                    uni.hideLoading();
                    uni.showToast({
                        title: `${e.name}连接成功`,
                        icon: 'success'
                    });

                    // 关闭设备选择弹窗
                    _this.closeDevice();

                    // 获取设备服务
                    _this.getBLEDeviceServicesForRefraction2(e.deviceId);
                },
                fail: (err) => {
                    console.error('createBLEConnection fail', err);
                    uni.hideLoading();
                    _this.handleConnectionFailure(e.deviceId, '连接失败');
                }
            });
        },

        /**
         * 获取BLE设备服务（屈光2专用）
         */
        getBLEDeviceServicesForRefraction2(deviceId) {
            const _this = this;

            uni.getBLEDeviceServices({
                deviceId,
                success: (res) => {
                    console.log('getBLEDeviceServices success', res);

                    // 查找主要服务
                    for (let i = 0; i < res.services.length; i++) {
                        if (res.services[i].isPrimary) {
                            _this.getBLEDeviceCharacteristicsForRefraction2(deviceId, res.services[i]
                                .uuid);
                            return;
                        }
                    }
                },
                fail: (err) => {
                    console.error('getBLEDeviceServices fail', err);
                    _this.showToast('获取设备服务失败');
                }
            });
        },

        /**
         * 获取BLE设备特征值（屈光2专用）
         */
        getBLEDeviceCharacteristicsForRefraction2(deviceId, serviceId) {
            const _this = this;

            uni.getBLEDeviceCharacteristics({
                deviceId,
                serviceId,
                success: (res) => {
                    console.log('getBLEDeviceCharacteristics success', res.characteristics);

                    // 保存连接参数
                    _this._deviceParams.serviceId = serviceId;

                    for (let i = 0; i < res.characteristics.length; i++) {
                        let item = res.characteristics[i];

                        // 处理可读特征
                        if (item.properties.read) {
                            uni.readBLECharacteristicValue({
                                deviceId,
                                serviceId,
                                characteristicId: item.uuid,
                            });
                        }

                        // 处理可写特征
                        if (item.properties.write) {
                            _this._deviceParams.writeCharacteristicId = item.uuid;
                            _this.writeBLECharacteristicValueForRefraction2();
                        }

                        // 处理通知特征
                        if (item.properties.notify || item.properties.indicate) {
                            uni.notifyBLECharacteristicValueChange({
                                deviceId,
                                serviceId,
                                characteristicId: item.uuid,
                                state: true,
                            });
                        }
                    }

                    // 设置数据监听
                    _this.setupBLECharacteristicValueChangeForRefraction2();
                },
                fail: (err) => {
                    console.error('getBLEDeviceCharacteristics fail', err);
                    _this.showToast('获取设备特征失败');
                }
            });
        },

        /**
         * 更新设备连接状态
         */
        updateDeviceConnectionStatus(deviceId, status) {
            this.deviceList = this.deviceList.map(item => {
                if (item.deviceId === deviceId) {
                    return {
                        ...item,
                        isConnected: status === 'connected',
                        connectionStatus: status // 'connecting', 'connected', 'disconnected'
                    };
                } else {
                    // 确保其他设备状态为未连接
                    return {
                        ...item,
                        isConnected: false,
                        connectionStatus: 'disconnected'
                    };
                }
            });
        },

        // 增强版特征值获取方法（支持错误重试机制）
        async _getCharacteristics(deviceId, serviceId) {
            try {
                const {
                    characteristics
                } = await new Promise((resolve, reject) => {
                    uni.getBLEDeviceCharacteristics({
                        deviceId,
                        serviceId,
                        success: (res) => {
                            if (res.errMsg !== 'getBLEDeviceCharacteristics:ok') {
                                return reject(new Error('特征值获取失败'));
                            }
                            // 特征值有效性验证
                            if (!res.characteristics || res.characteristics.length === 0) {
                                return reject(new Error('未发现可用特征值'));
                            }
                            resolve(res);
                        },
                        fail: (err) => reject(err)
                    });
                });

                // 调试日志输出（生产环境需关闭）
                console.log('[BLE Characteristics]',
                    characteristics.map(c => ({
                        uuid: c.uuid,
                        properties: c.properties
                    }))
                );

                return characteristics;
            } catch (error) {
                // 自动重试机制（最多3次）
                if (this._retryCount < 3) {
                    this._retryCount++;
                    await new Promise(resolve => setTimeout(resolve, 500));
                    return this._getCharacteristics(deviceId, serviceId);
                }
                throw new Error(`特征值获取失败: ${error.message}`);
            }
        },

        decode(buffer) {
            const bytes = new Uint8Array(buffer);
            let str = '';

            for (let i = 0; i < bytes.length;) {
                const byte1 = bytes[i++];

                if (byte1 < 0x80) {
                    str += String.fromCharCode(byte1);
                } else if (byte1 >= 0xC0 && byte1 < 0xE0 && i < bytes.length) {
                    const byte2 = bytes[i++];
                    str += String.fromCharCode(
                        ((byte1 & 0x1F) << 6) | (byte2 & 0x3F)
                    );
                } else if (byte1 >= 0xE0 && byte1 < 0xF0 && i + 1 < bytes.length) {
                    const byte2 = bytes[i++];
                    const byte3 = bytes[i++];
                    str += String.fromCharCode(
                        ((byte1 & 0x0F) << 12) |
                        ((byte2 & 0x3F) << 6) |
                        (byte3 & 0x3F)
                    );
                } else {
                    str += '�'; // 替换无法解码的字符
                }
            }
            return str;
        },

        _processRS232Data(buffer) {
            const str = this.decode(buffer)
            if (!str.includes('}')) {
                this.jsonStr += str
            } else {
                this.jsonStr += str
                this.$store.dispatch('updateDiopterData', JSON.parse(this.jsonStr));
                this.jsonStr = ''
            }
        },

        _reconnect() {
            if (this._reconnectCount < 3) {
                this._reconnectCount++;
                setTimeout(async () => {
                    try {
                        await this.deviceSelect({
                            deviceId: this._deviceParams.deviceId,
                            name: this._deviceParams.name
                        });
                    } catch (e) {
                        this._reconnect();
                    }
                }, 2000);
            }
        },

        _updateDeviceInfo(device) {
            if (!this._deviceParams) return;

            this._deviceParams.lastConnected = new Date().toISOString();
            this._deviceParams.signalStrength = device.RSSI;

            // 保存设备信息到本地存储
            try {
                uni.setStorageSync('btDevice', JSON.stringify({
                    ...this._deviceParams,
                    secure: this._isSecureConnection()
                }));
            } catch (error) {
                console.error('保存设备信息失败:', error);
            }

            // 更新设备列表状态（使用统一的状态更新方法）
            this.updateDeviceConnectionStatus(device.deviceId, 'connected');
        },

        _isSecureConnection() {
            const deviceInfo = uni.getConnectedBluetoothDevices()[0] || {}
            const isBonded = deviceInfo.bonded || false // Android配对状态

            const characteristics = this._deviceParams?.characteristics || []
            const hasEncryption = characteristics.some(c => {
                console.log('[Security Check] Characteristic:', {
                    uuid: c.uuid,
                    properties: c.properties
                })
                return c.properties.encryptable || c.properties.authenticated
            })

            const crcValid = true // 临时跳过CRC验证

            return isBonded && hasEncryption && crcValid
        },

        async _sendInitSequence() {
            // 发送流控制指令（RTS/CTS）
            const flowControlCmd = new Uint8Array([0x41, 0x54, 0x2B, 0x46, 0x4C, 0x4F, 0x57, 0x3D, 0x31, 0x0D,
                0x0A
            ]); // AT+FLOW=1
            await this._writeBLEValue(flowControlCmd);

            // 设置波特率（示例：115200）
            const baudRateCmd = new Uint8Array([0x41, 0x54, 0x2B, 0x42, 0x41, 0x55, 0x44, 0x3D, 0x31, 0x31, 0x35,
                0x32, 0x30, 0x30, 0x0D, 0x0A
            ]); // AT+BAUD=115200
            await this._writeBLEValue(baudRateCmd);

            await new Promise(resolve => setTimeout(resolve, 300));
        },

        async _writeBLEValue(buffer) {
            return new Promise((resolve, reject) => {
                uni.writeBLECharacteristicValue({
                    deviceId: this._deviceParams.deviceId,
                    serviceId: this._deviceParams.serviceId,
                    characteristicId: this._deviceParams.writeCharId,
                    value: buffer.buffer ? buffer.buffer : new Uint8Array(buffer).buffer,
                    success: resolve,
                    fail: reject
                });
            });
        },

        _createConnection(deviceId) {
            return new Promise((resolve, reject) => {
                // 尝试BLE连接
                uni.createBLEConnection({
                    deviceId,
                    success: resolve,
                    fail: () => {
                        // 若失败尝试经典蓝牙连接
                        uni.connectSocket({
                            url: `bluetooth://${deviceId}?protocol=rfcomm&channel=1`,
                            protocols: ['binary'],
                            success: resolve,
                            fail: reject
                        });
                    }
                });
            });
        },

        // 辅助方法：获取蓝牙服务
        _getBLEServices(deviceId) {
            return new Promise((resolve, reject) => {
                uni.getBLEDeviceServices({
                    deviceId,
                    success: (res) => resolve(res.services),
                    fail: (err) => reject(err)
                });
            });
        },

        // 辅助方法：获取特征值
        _getBLECharacteristics(deviceId, serviceId) {
            return new Promise((resolve, reject) => {
                uni.getBLEDeviceCharacteristics({
                    deviceId,
                    serviceId,
                    success: (res) => resolve(res.characteristics),
                    fail: (err) => reject(err)
                });
            });
        },

        _resetCache() {
            this.bleState.chunkCache.forEach((_, key) => this.bleState.chunkCache.delete(key));
            this.bleState.currentBuffer = [];
        },

        _startNotification() {
            uni.notifyBLECharacteristicValueChange({
                deviceId: this._deviceParams.deviceId,
                serviceId: this._deviceParams.serviceId,
                characteristicId: this._deviceParams.writeCharId,
                state: true,
                success: () => {
                    uni.onBLECharacteristicValueChange(res => {
                        if (res.deviceId === this._deviceParams.deviceId &&
                            res.characteristicId === this._deviceParams.writeCharId) {
                            this._processRS232Data(res.value);
                        }
                    });

                    this._heartbeatTimer = setInterval(() => {
                        this._sendHeartbeat();
                    }, 5000);
                },
                fail: () => {
                    uni.$u.toast('数据通道初始化失败');
                    this._reconnect(); // 自动重连机制
                }
            });

            // #ifdef APP-PLUS
            plus.bluetooth.setBLEMTU({
                deviceId: this._deviceParams.deviceId,
                mtu: 251,
                fail: () => console.warn('MTU协商失败')
            });
            // #endif
        },

        _sendHeartbeat() {
            const heartbeatData = new Uint8Array([0x01, 0xFE])
            this._writeBLEValue(heartbeatData).catch(() => {
                this._reconnect()
            })
        },

        async openSchoolShow() {
            const res = await getSchoolList()
            this.schoolList = res.data.data
            this.schoolShow = true
        },

        // 新增：统一权限检查方法
        async checkBluetoothPermissions() {
            try {
                // Android必须获取定位权限
                if (this.isAndroid) {
                    const locationSetting = await this.getAuthSetting('scope.userLocation')
                    if (!locationSetting) {
                        const result = await this.requestLocationPermission();
                        if (!result) return false; // 用户拒绝了定位权限
                    }
                }

                // 所有平台都需要蓝牙权限
                const bluetoothGranted = await this.checkPermissionStatus('scope.bluetooth');
                if (!bluetoothGranted) {
                    const result = await this.requestBluetoothPermission();
                    if (!result) return false; // 用户拒绝了蓝牙权限
                }

                return true
            } catch (error) {
                console.error('权限检查失败:', error)
                return false
            }
        },

        // 检查权限状态
        checkPermissionStatus(scope) {
            return new Promise(resolve => {
                uni.getSetting({
                    success: (res) => {
                        // 处理微信小程序的权限键名格式
                        const key = scope.startsWith('scope.') ? scope.substring(6) : scope;
                        resolve(!!res.authSetting[key]);
                    },
                    fail: () => resolve(false)
                });
            });
        },

        // 新增：获取权限设置状态
        getAuthSetting(scope) {
            return new Promise((resolve) => {
                uni.getSetting({
                    success: (res) => {
                        resolve(res.authSetting[scope] || false)
                    },
                    fail: () => resolve(false)
                })
            })
        },

        // 新增：请求定位权限
        requestLocationPermission() {
            return new Promise((resolve) => {
                uni.authorize({
                    scope: 'scope.userLocation',
                    success: () => resolve(true),
                    fail: (err) => {
                        console.error('定位权限请求失败:', err);
                        this.showPermissionGuide('定位', '用于发现蓝牙设备');
                        resolve(false);
                    }
                });
            });
        },

        // 新增：请求蓝牙权限
        requestBluetoothPermission() {
            return new Promise((resolve) => {
                uni.authorize({
                    scope: 'scope.bluetooth',
                    success: () => resolve(true),
                    fail: (err) => {
                        console.error('蓝牙权限请求失败:', err);
                        this.showPermissionGuide('蓝牙', '用于连接检测设备');
                        resolve(false);
                    }
                });
            });
        },

        // 新增：权限引导弹窗
        showPermissionGuide(permissionName, usage) {
            uni.showModal({
                title: `${permissionName}权限未开启`,
                content: `请在小程序设置中开启${permissionName}权限${usage ? '，' + usage : ''}`,
                confirmText: '立即开启',
                cancelText: '稍后再说',
                success: (res) => {
                    if (res.confirm) {
                        // 直接打开小程序设置页
                        uni.openSetting({
                            success: (settingRes) => {
                                console.log('设置页打开成功', settingRes);
                            },
                            fail: (err) => {
                                console.error('打开设置页失败:', err);
                                this.showToast('打开设置失败，请手动前往小程序设置开启权限');
                            }
                        });
                    }
                }
            });
        },


        /**
         * 优化后的设备扫描方法
         */
        async openDeviceShow() {
            try {
                // 防止重复扫描
                if (this.bluetoothState.isScanning) {
                    this.showToast('正在扫描中，请稍候...');
                    return;
                }

                // 步骤1：检查权限
                const hasPermissions = await this.checkBluetoothPermissions();
                if (!hasPermissions) {
                    this.showPermissionModal();
                    return;
                }

                // 步骤2：初始化蓝牙适配器
                await this.initBluetoothAdapter();

                // 步骤3：开始扫描设备
                await this.startDeviceDiscovery();

            } catch (error) {
                this.handleBluetoothInitError(error);
            }
        },

        /**
         * 显示权限授权弹窗
         */
        showPermissionModal() {
            uni.showModal({
                title: '无法使用蓝牙',
                content: '您拒绝了蓝牙权限，将无法使用设备扫描功能',
                confirmText: '重新授权',
                success: (res) => {
                    if (res.confirm) this.openDeviceShow();
                }
            });
        },

        /**
         * 开始设备发现流程
         */
        async startDeviceDiscovery() {
            this.bluetoothState.isScanning = true;
            this.deviceList = []; // 清空之前的设备列表

            try {
                // 开始扫描
                await this.startBluetoothScan();

                // 设置扫描监听
                this.setupDeviceFoundListener();

                // 显示扫描界面
                this.deviceShow = true;

                // 设置扫描超时
                this.setScanTimeout();

            } catch (error) {
                this.bluetoothState.isScanning = false;
                throw error;
            }
        },

        /**
         * 启动蓝牙扫描（优化版）
         */
        startBluetoothScan() {
            return new Promise((resolve, reject) => {
                // 优化扫描参数以提高稳定性
                uni.startBluetoothDevicesDiscovery({
                    allowDuplicatesKey: true, // 允许重复发现，提高稳定性
                    interval: 0, // 设置为0以获得最快的扫描速度
                    powerLevel: 'high', // 使用高功率扫描
                    success: () => {
                        console.log('蓝牙扫描启动成功');
                        uni.showLoading({
                            title: '搜索设备中...',
                            mask: true
                        });
                        resolve();
                    },
                    fail: (err) => {
                        console.error('蓝牙扫描启动失败:', err);
                        reject(err);
                    }
                });
            });
        },

        /**
         * 设置设备发现监听器
         */
        setupDeviceFoundListener() {
            // 移除之前的监听器
            if (this.bluetoothState.scanListener) {
                uni.offBluetoothDeviceFound(this.bluetoothState.scanListener);
            }

            this.bluetoothState.scanListener = uni.onBluetoothDeviceFound(devices => {
                const validDevices = this.filterValidDevices(devices.devices);
                this.addNewDevices(validDevices);
            });
        },

        /**
         * 过滤有效设备（优化版）
         */
        filterValidDevices(devices) {
            return devices
                .map(item => ({
                    ...item,
                    isConnected: item.connected || false,
                    // 添加信号强度信息用于排序
                    rssi: item.RSSI || -100
                }))
                .filter(item => {
                    // 放宽过滤条件以提高设备发现率

                    // 1. 基本连接性检查（保留）
                    if (!item.connectable) return false;

                    // 2. 信号强度过滤（过滤信号过弱的设备）
                    if (item.RSSI && item.RSSI < -80) return false;

                    // 3. 设备名称检查（大幅放宽条件）
                    if (item.name) {
                        // 包含常见蓝牙设备名称关键词
                        const nameKeywords = [
                            'RS232', 'HC-', 'ESP', 'BLE', 'UART', 'Serial',
                            'AT-', 'JDY', 'DX-', 'MLT', 'CC254', 'nRF',
                            '蓝牙', 'Bluetooth', 'SPP', 'COM', 'RFCOMM'
                        ];

                        const hasValidName = nameKeywords.some(keyword =>
                            item.name.toUpperCase().includes(keyword.toUpperCase())
                        );

                        if (hasValidName) return true;
                    }

                    // 4. 服务UUID检查（更宽松）
                    if (item.advertisServiceUUIDs && item.advertisServiceUUIDs.length > 0) {
                        // 检查是否包含串口相关的服务UUID
                        const sppUUIDs = [
                            '1101', '1102', 'FFE0', 'FFE1', 'FFF0', 'FFF1'
                        ];

                        const hasValidService = item.advertisServiceUUIDs.some(uuid =>
                            sppUUIDs.some(sppUuid =>
                                uuid.toUpperCase().includes(sppUuid.toUpperCase())
                            )
                        );

                        if (hasValidService) return true;
                    }

                    // 5. 制造商数据检查
                    if (item.manufacturerData && item.manufacturerData.byteLength > 0) {
                        return true;
                    }

                    // 6. 如果有设备名但不在关键词列表中，也保留（用户可能需要）
                    if (item.name && item.name.trim().length > 0) {
                        return true;
                    }

                    return false;
                })
                // 按信号强度排序，信号强的设备排在前面
                .sort((a, b) => (b.rssi || -100) - (a.rssi || -100));
        },

        /**
         * 添加新发现的设备（优化版）
         */
        addNewDevices(validDevices) {
            let hasNewDevices = false;

            validDevices.forEach(newDevice => {
                const existingIndex = this.deviceList.findIndex(existDevice =>
                    existDevice.deviceId === newDevice.deviceId
                );

                if (existingIndex === -1) {
                    // 新设备，直接添加
                    this.deviceList.push(newDevice);
                    hasNewDevices = true;
                    console.log('发现新设备:', newDevice.name || newDevice.deviceId);
                } else {
                    // 已存在的设备，更新信息（特别是信号强度）
                    const existingDevice = this.deviceList[existingIndex];

                    // 更新设备信息，保持连接状态
                    this.deviceList.splice(existingIndex, 1, {
                        ...newDevice,
                        isConnected: existingDevice.isConnected,
                        connectionStatus: existingDevice.connectionStatus
                    });
                }
            });

            // 如果有新设备，重新排序设备列表
            if (hasNewDevices) {
                this.deviceList.sort((a, b) => (b.rssi || -100) - (a.rssi || -100));
            }
        },

        /**
         * 设置扫描超时（优化版）
         */
        setScanTimeout() {
            // 延长扫描时间以提高设备发现率
            const extendedTimeout = this.BLUETOOTH_CONFIG.SCAN_TIMEOUT * 2; // 20秒

            this.scanTimeoutId = setTimeout(() => {
                console.log(`扫描超时 (${extendedTimeout}ms)，停止扫描`);
                this.stopDeviceDiscovery();

                // 如果没有发现任何设备，提示用户
                if (this.deviceList.length === 0) {
                    uni.showModal({
                        title: '未发现设备',
                        content: '未发现可连接的蓝牙设备，请确保：\n1. 设备已开启并处于可发现状态\n2. 手机蓝牙已开启\n3. 设备距离足够近\n\n是否重新扫描？',
                        confirmText: '重新扫描',
                        cancelText: '取消',
                        success: (res) => {
                            if (res.confirm) {
                                // 重新扫描
                                setTimeout(() => {
                                    this.startDeviceDiscovery();
                                }, 1000);
                            }
                        }
                    });
                } else {
                    this.showToast(`发现 ${this.deviceList.length} 个设备`);
                }
            }, extendedTimeout);
        },

        /**
         * 停止设备发现（优化版）
         */
        stopDeviceDiscovery() {
            console.log('停止设备扫描');

            this.bluetoothState.isScanning = false;
            uni.hideLoading();

            // 清除扫描超时定时器
            if (this.scanTimeoutId) {
                clearTimeout(this.scanTimeoutId);
                this.scanTimeoutId = null;
            }

            // 停止蓝牙设备发现
            uni.stopBluetoothDevicesDiscovery({
                success: () => {
                    console.log('蓝牙扫描停止成功');
                },
                fail: (err) => {
                    console.error('蓝牙扫描停止失败:', err);
                }
            });

            // 移除设备发现监听器
            if (this.bluetoothState.scanListener) {
                uni.offBluetoothDeviceFound(this.bluetoothState.scanListener);
                this.bluetoothState.scanListener = null;
            }
        },

        /**
         * 重置连接状态
         */
        _resetConnection() {
            const previousDeviceId = this._deviceParams?.deviceId;

            this._deviceParams = null;
            this.bluetoothState.isConnected = false;
            this.bluetoothState.isConnecting = false;
            this.bluetoothState.currentDevice = null;

            // 清除定时器
            if (this.heartbeatTimer) {
                clearInterval(this.heartbeatTimer);
                this.heartbeatTimer = null;
            }

            if (this.reconnectTimer) {
                clearTimeout(this.reconnectTimer);
                this.reconnectTimer = null;
            }

            // 重置设备列表中的连接状态
            if (previousDeviceId) {
                this.updateDeviceConnectionStatus(previousDeviceId, 'disconnected');
            }

            // 清除设备名称
            this.model1.userInfo.deviceName = '';
        },

        /**
         * 设置BLE特征值变化监听（屈光2专用）
         */
        setupBLECharacteristicValueChangeForRefraction2() {
            const _this = this;

            // 将数据缓存移到组件实例上
            if (!this.bleDataCache) {
                this.bleDataCache = {
                    bleData: '',
                    bleResponseData: ''
                };
            }

            uni.onBLECharacteristicValueChange((characteristic) => {
                console.log('BLE特征值变化:', characteristic);

                // 将ArrayBuffer转换为16进制字符串
                const hexString = _this.arrayBufferToHex(characteristic.value);
                const stringData = _this.hexToString(hexString);

                if (_this.isResponseMode) {
                    // 响应模式处理
                    if (!_this.bleDataCache.bleResponseData) {
                        _this.bleDataCache.bleResponseData = stringData;
                    } else {
                        _this.bleDataCache.bleResponseData += stringData;
                    }

                    if (_this.bleDataCache.bleResponseData.indexOf("{") !== -1 && _this.bleDataCache
                        .bleResponseData.indexOf("}") !== -1) {
                        try {
                            let jsonStr = _this.bleDataCache.bleResponseData.replace(/\s/g, "").replace(
                                /\ufeff/g, "");
                            let jsonObj = JSON.parse(jsonStr);

                            if (_this.bleDataCache.bleResponseData.indexOf("Msg") >= 0) {
                                uni.showToast({
                                    title: 'Msg：' + jsonObj.Msg,
                                    icon: 'success',
                                    duration: 2000
                                });
                            }

                            _this.isResponseMode = false;
                            _this.bleDataCache.bleResponseData = '';
                        } catch (error) {
                            console.error('解析响应数据失败:', error);
                        }
                    }
                } else {
                    // 数据接收模式
                    if (!_this.bleDataCache.bleData) {
                        _this.bleDataCache.bleData = stringData;
                    } else {
                        _this.bleDataCache.bleData += stringData;
                    }

                    // 检查数据完整性并处理
                    const processResult = _this.processBLEDataForRefraction2(_this.bleDataCache.bleData);
                    if (processResult) {
                        _this.bleDataCache.bleData = ''; // 处理完成后清空缓存
                    }
                }
            });
        },

        /**
         * 处理BLE数据（屈光2专用）
         * @param {string} bleData - 接收到的蓝牙数据
         * @returns {boolean} - 是否处理完成（需要清空缓存）
         */
        processBLEDataForRefraction2(bleData) {
            // 参考index.js的BleDataCheck方法
            if (bleData.indexOf("SW") !== -1 && bleData.indexOf("FE") !== -1) {
                console.log("SW_____FE数据处理");

                const eyeType = bleData.substring(21, 22);
                console.log('眼别类型:', eyeType);
                const diopterData = {
                    pupilDistance: null,
                    rightPd: null,
                    leftPd: null,
                    rightSph: null,
                    leftSph: null,
                    rightCyl: null,
                    leftCyl: null,
                    rightAx: null,
                    leftAx: null,
                }
                if (eyeType == 1) {
                    diopterData.rightPd = bleData.substring(40, 43)
                    diopterData.rightSph = bleData.substring(43, 48)
                    diopterData.rightCyl = bleData.substring(53, 58)
                    diopterData.rightAx = bleData.substring(58, 61)
                    diopterData.pupilDistance = bleData.substring(79, 81)

                } else if (eyeType == 2) {
                    diopterData.leftPd = bleData.substring(40, 43)
                    diopterData.leftSph = bleData.substring(43, 48)
                    diopterData.leftCyl = bleData.substring(53, 58)
                    diopterData.leftAx = bleData.substring(58, 61)
                    diopterData.pupilDistance = bleData.substring(79, 81)
                }

                // const diopterData = {
                // 	eyeType: eyeType,
                // 	pupil: bleData.substring(40, 43),
                // 	sphRight: bleData.substring(43, 48),
                // 	DS1: bleData.substring(48, 53),
                // 	DC1: bleData.substring(53, 58),
                // 	Axis1: bleData.substring(58, 61),
                // 	sphRight: bleData.substring(61, 66),
                // 	DS2: bleData.substring(66, 71),
                // 	DC2: bleData.substring(71, 76),
                // 	Axis2: bleData.substring(76, 79),
                // 	pd: bleData.substring(79, 81),
                // 	GazeH: this.formatGazeHV(bleData.substring(85, 88), false),
                // 	GazeV: this.formatGazeHV(bleData.substring(88, 91), true)
                // };

                // 存储到Vuex
                this.$store.dispatch('updateDiopterData', diopterData);

                // 发送SUCC确认
                this.writeBLECharacteristicValueForRefraction2();

                // return true; // 处理完成

            } else
                if (bleData.indexOf("FE") === -1 && bleData.indexOf("{") !== -1 && bleData.indexOf("}") !== -1) {
                    // 纯JSON数据处理
                    console.log("纯JSON数据处理");
                    try {
                        let jsonStr = bleData.replace(/\s/g, "").replace(/\ufeff/g, "");
                        let jsonObj = JSON.parse(jsonStr);
                        let clinicResult = jsonObj.ClinicResult[0];

                        const diopterData = {
                            rightPd: clinicResult.RPupil,
                            leftPd: clinicResult.LPupil,
                            pupilDistance: clinicResult.PD,
                            // rGazeV: this.formatGazeHV(clinicResult.RGazeV, true),
                            // lGazeV: this.formatGazeHV(clinicResult.LGazeV, true),
                            // rGazeH: this.formatGazeHV(clinicResult.RGazeH, false),
                            // lGazeH: this.formatGazeHV(clinicResult.LGazeH, false),
                            // rSE1: clinicResult.RSE1,
                            // RDS1: clinicResult.RDS1,
                            // rDC1: clinicResult.RDC1,
                            // rAxis1: clinicResult.RAxis1,
                            // rSE2: clinicResult.RSE2,
                            rightSph: clinicResult.RDS2,
                            rightCyl: clinicResult.RDC2,
                            rightAx: clinicResult.RAxis2,
                            // lSE1: clinicResult.LSE1,
                            // lDS1: clinicResult.LDS1,
                            // lDC1: clinicResult.LDC1,
                            // lAxis1: clinicResult.LAxis1,
                            // lSE2: clinicResult.LSE2,
                            leftSph: clinicResult.LDS2,
                            leftCyl: clinicResult.LDC2,
                            leftAx: clinicResult.leftAx,
                        };

                        // 存储到Vuex
                        this.$store.dispatch('updateDiopterData', diopterData);

                        // 发送SUCC确认
                        this.writeBLECharacteristicValueForRefraction2();

                        return true; // 处理完成

                    } catch (error) {
                        uni.$u.toast('解析JSON数据失败');
                        console.error('解析JSON数据失败:', error);
                        return false; // 处理失败，不清空缓存
                    }
                }

            return false; // 数据不完整，继续接收
        },

        /**
         * 写入BLE特征值（屈光2专用）- 发送SUCC
         */
        writeBLECharacteristicValueForRefraction2() {
            if (!this._deviceParams || !this._deviceParams.writeCharacteristicId) {
                console.error('设备参数不完整，无法写入数据');
                return;
            }

            const successData = this.stringToArrayBuffer('SUCC');

            uni.writeBLECharacteristicValue({
                deviceId: this._deviceParams.deviceId,
                serviceId: this._deviceParams.serviceId,
                characteristicId: this._deviceParams.writeCharacteristicId,
                value: successData,
                success: () => {
                    console.log('SUCC发送成功');
                },
                fail: (err) => {
                    console.error('SUCC发送失败:', err);
                }
            });
        },

        /**
         * ArrayBuffer转16进制字符串
         */
        arrayBufferToHex(buffer) {
            const hexArr = Array.prototype.map.call(
                new Uint8Array(buffer),
                function (bit) {
                    return ('00' + bit.toString(16)).slice(-2);
                }
            );
            return hexArr.join('');
        },

        /**
         * 16进制字符串转字符串
         */
        hexToString(str) {
            let val = "";
            const len = str.length / 2;
            for (let i = 0; i < len; i++) {
                val += String.fromCharCode(parseInt(str.substr(i * 2, 2), 16));
            }
            return this.utf8to16(val);
        },

        /**
         * UTF8转UTF16（处理中文乱码）
         */
        utf8to16(str) {
            let out = "";
            const len = str.length;
            let i = 0;

            while (i < len) {
                const c = str.charCodeAt(i++);
                switch (c >> 4) {
                    case 0:
                    case 1:
                    case 2:
                    case 3:
                    case 4:
                    case 5:
                    case 6:
                    case 7:
                        out += str.charAt(i - 1);
                        break;
                    case 12:
                    case 13:
                        const char2 = str.charCodeAt(i++);
                        out += String.fromCharCode(((c & 0x1F) << 6) | (char2 & 0x3F));
                        break;
                    case 14:
                        const char2_2 = str.charCodeAt(i++);
                        const char3 = str.charCodeAt(i++);
                        out += String.fromCharCode(((c & 0x0F) << 12) |
                            ((char2_2 & 0x3F) << 6) |
                            ((char3 & 0x3F) << 0));
                        break;
                }
            }
            return out;
        },

        /**
         * 字符串转ArrayBuffer
         */
        stringToArrayBuffer(str) {
            const array = new Uint8Array(str.length);
            for (let i = 0; i < str.length; i++) {
                array[i] = str.charCodeAt(i);
            }
            return array.buffer;
        },

        /**
         * 格式化注视方向（处理方向显示）
         */
        formatGazeHV(str, isGazeV = true) {
            if (str === undefined || str == null || str === '' || str === 'undefined') return '';
            if (str == 0) return str;

            if (isGazeV) {
                if (str < 0) {
                    return "⬆" + Math.abs(str);
                } else {
                    return "⬇" + Math.abs(str);
                }
            } else {
                if (str < 0) {
                    return "➡" + Math.abs(str);
                } else {
                    return "⬅" + Math.abs(str);
                }
            }
        },

        /**
         * 手动刷新设备列表
         */
        async refreshDeviceList() {
            if (this.bluetoothState.isScanning) {
                this.showToast('正在扫描中，请稍候...');
                return;
            }

            try {
                // 先停止当前扫描（如果有）
                this.stopDeviceDiscovery();

                // 清空设备列表
                this.deviceList = [];

                // 短暂延迟后重新开始扫描
                await new Promise(resolve => setTimeout(resolve, 500));

                // 重新开始扫描
                await this.startDeviceDiscovery();

                this.showToast('开始重新扫描设备...');

            } catch (error) {
                console.error('刷新设备列表失败:', error);
                this.showToast('刷新失败，请重试');
            }
        },

        /**
         * 显示Toast消息
         */
        showToast(message, icon = 'none') {
            uni.showToast({
                title: message,
                icon: icon,
                duration: 2000
            });
        },

        /**
         * 解析制造商数据
         */
        _parseManufacturerData(data) {
            if (!data || data.length === 0) return 'Unknown';

            // 简单的制造商ID解析
            const manufacturerId = data[0] | (data[1] << 8);
            const manufacturers = {
                0x004C: 'Apple',
                0x0006: 'Microsoft',
                0x00E0: 'Google',
                0x0075: 'Samsung'
            };

            return manufacturers[manufacturerId] || `Unknown (${manufacturerId.toString(16)})`;
        },

        /**
         * 优化的设备连接状态监听
         */
        setupConnectionStateListener() {
            if (this.bluetoothState.connectionListener) {
                uni.offBLEConnectionStateChange(this.bluetoothState.connectionListener);
            }

            this.bluetoothState.connectionListener = uni.onBLEConnectionStateChange(res => {
                console.log('连接状态变化:', res);

                // 更新设备列表中的连接状态
                this.deviceList = this.deviceList.map(item => ({
                    ...item,
                    isConnected: item.deviceId === res.deviceId ? res.connected : item.isConnected
                }));

                // 更新全局连接状态
                if (this._deviceParams && this._deviceParams.deviceId === res.deviceId) {
                    this.bluetoothState.isConnected = res.connected;
                    this.bluetoothState.currentDevice = res.connected ? this._deviceParams : null;

                    if (!res.connected) {
                        this.showToast('设备连接已断开');
                        this._resetConnection();

                        // 自动重连逻辑
                        if (this._reconnectCount < this.BLUETOOTH_CONFIG.MAX_RETRY_COUNT) {
                            this._reconnect();
                        }
                    }
                }
            });
        },

        /**
         * 清理蓝牙资源
         */
        cleanupBluetoothResources() {
            // 停止扫描
            if (this.bluetoothState.isScanning) {
                this.stopDeviceDiscovery();
            }

            // 清除监听器
            if (this.bluetoothState.scanListener) {
                uni.offBluetoothDeviceFound(this.bluetoothState.scanListener);
                this.bluetoothState.scanListener = null;
            }

            if (this.bluetoothState.connectionListener) {
                uni.offBLEConnectionStateChange(this.bluetoothState.connectionListener);
                this.bluetoothState.connectionListener = null;
            }

            // 清除定时器
            if (this.heartbeatTimer) {
                clearInterval(this.heartbeatTimer);
                this.heartbeatTimer = null;
            }

            if (this.reconnectTimer) {
                clearTimeout(this.reconnectTimer);
                this.reconnectTimer = null;
            }

            // 断开连接
            if (this._deviceParams && this.bluetoothState.isConnected) {
                uni.closeBLEConnection({
                    deviceId: this._deviceParams.deviceId,
                    success: () => console.log('蓝牙连接已关闭'),
                    fail: (err) => console.error('关闭蓝牙连接失败:', err)
                });
            }

            // 重置状态
            this._resetConnection();
        },

        // 新增：蓝牙适配器初始化
        initBluetoothAdapter() {
            return new Promise((resolve, reject) => {
                uni.openBluetoothAdapter({
                    success: (res) => {
                        console.log('蓝牙适配器初始化成功:', res);
                        this.bluetoothState.adapterInitialized = true;
                        resolve(res);
                    },
                    fail: (err) => {
                        console.error('蓝牙适配器初始化失败:', err);
                        this.bluetoothState.adapterInitialized = false;

                        // 增加特定错误代码处理
                        if (err.errCode === 10001) {
                            uni.showToast({
                                title: '请开启手机蓝牙',
                                icon: 'none'
                            });
                        } else if (err.errCode === 10004) {
                            uni.showToast({
                                title: '不支持蓝牙功能',
                                icon: 'none'
                            });
                        } else {
                            uni.showToast({
                                title: '蓝牙初始化失败',
                                icon: 'none'
                            });
                        }
                        reject(err);
                    }
                });
            });
        },

        // 新增：蓝牙初始化错误处理
        handleBluetoothInitError(err) {
            console.error('蓝牙初始化失败:', err)

            // 根据错误码提供精准引导
            switch (err.errCode) {
                case 10001: // 蓝牙未开启
                    uni.showModal({
                        title: '蓝牙未开启',
                        content: '请开启手机蓝牙功能',
                        confirmText: '去开启',
                        success: ({
                            confirm
                        }) => {
                            if (confirm) uni.openBluetoothAdapter()
                        }
                    })
                    break
                case 10002: // 定位未开启（仅Android）
                    if (this.isAndroid) {
                        uni.showModal({
                            title: '定位服务未开启',
                            content: '安卓系统需要开启定位服务才能搜索蓝牙设备',
                            confirmText: '去开启',
                            success: ({
                                confirm
                            }) => {
                                if (confirm) uni.openLocationSetting()
                            }
                        })
                    }
                    break
                default:
                    uni.showToast({
                        title: `蓝牙初始化失败[${err.errCode}]`,
                        icon: 'none'
                    })
            }
        },

        onSchoolSearch(value) {
            this.schoolKeyword = value;
        },
        onDeviceSearch(value) {
            this.deviceKeyword = value;
        },
        startCheckCode() {
            if (!this.model1.userInfo.checkType) {
                uni.$u.toast('请先选择检查项目');
                return
            }
            if (!this.model1.userInfo.schoolId) {
                uni.$u.toast('请先选择学校');
                return
            }
            if (!this.model1.userInfo.deviceName && this.model1.userInfo.checkType != 1) {
                uni.$u.toast('请先选择设备');
                return
            }
            // if (this.model1.userInfo.checkType == 1) {
            uni.scanCode({
                success: (res) => {
                    getStudentById(res.result).then(response => {
                        if (response.data.data) {
                            const student = response.data.data
                            if (student.schoolId != this.model1.userInfo.schoolId) {
                                uni.$u.toast('学生与学校不匹配');
                                return
                            }
                            if (this.model1.userInfo.checkType == 1) {
                                uni.navigateTo({
                                    url: `/pageA/scCheck/visionFormIndex?psnId=${student.id}&psnName=${student.name}`
                                })
                            } else {
                                uni.navigateTo({
                                    url: `/pageA/scCheck/diopterFormIndex?psnId=${student.id}&psnName=${student.name}`
                                })
                            }

                        } else {
                            uni.$u.toast('请扫描正确的学生二维码');
                        }
                    })

                },
                fail: (err) => {
                    console.log('扫一扫失败：', err);
                }
            });
            // }
            // if (this.model1.userInfo.checkType == 2) {
            // 	uni.scanCode({
            // 		success: (res) => {
            // 			getStudentById(res.result).then(response => {
            // 				if (response.data.data) {
            // 					uni.navigateTo({
            // 						url: "/pageA/scCheck/diopterFormIndex?psnId=" +
            // 							response
            // 							.data.data.id + '&psnName=' + response
            // 							.data.data
            // 							.name
            // 					})
            // 				} else {
            // 					uni.$u.toast('请扫描正确的学生二维码');
            // 				}
            // 			})

            // 		},
            // 		fail: (err) => {
            // 			console.log('扫一扫失败：', err);
            // 		}
            // 	});
            // 	return;
            // }
        },
    }
}
</script>

<style lang="less" scoped>
.bg {
    width: 100%;
    background: linear-gradient(180deg, #e3f3f0 0%, #f8fcff 100%);
    position: relative;
    overflow: hidden;

    &::after {
        content: '';
        position: absolute;
        bottom: -20rpx;
        left: 0;
        width: 100%;
        height: 40rpx;
        background: #ffffff;
        border-radius: 40rpx 40rpx 0 0;
    }
}

.bg-img {
    width: 100%;
    height: 360rpx;
    transform: scale(1.2);
    opacity: 0.9;
}

.form {
    margin-top: 40rpx;
    padding: 40rpx;
    background: #ffffff;
    border-radius: 40rpx 40rpx 0 0;
    box-shadow: 0 -10rpx 30rpx rgba(58, 119, 113, 0.05);

    /deep/ .u-form-item {
        padding: 28rpx 0;
        margin: 10rpx 0;
        border-radius: 12rpx;
        transition: all 0.3s;

        &:active {
            background: #f8f8f8;
        }

        .u-form-item__body {
            padding: 0 20rpx;
        }

        .u-icon {
            color: #999;
            margin-left: 20rpx;
        }
    }
}

.action-sheet-list {
    max-height: 60vh;
    padding: 0 30rpx;
}

.action-sheet-item {
    padding: 32rpx 40rpx;
    font-size: 30rpx;
    color: #333;
    border-bottom: 1rpx solid #f0f0f0;
    transition: all 0.2s;
    position: relative;

    &::after {
        content: '';
        position: absolute;
        left: 40rpx;
        right: 40rpx;
        bottom: 0;
        height: 1rpx;
        background: #f5f5f5;
    }

    &:last-child::after {
        display: none;
    }
}

.item-hover {
    background: #f8f8f8;
    transform: translateX(10rpx);
}

.empty-tip {
    text-align: center;
    padding: 60rpx 0;
    color: #ccc;
    font-size: 28rpx;
}

.startCheck {
    margin-top: 80rpx;
    padding: 0 20rpx;
    position: relative;

    /deep/ .u-button {
        height: 88rpx;
        border-radius: 16rpx;
        font-size: 32rpx;
        letter-spacing: 2rpx;
        background: linear-gradient(45deg, #4dccc6, #76e0d9);
        box-shadow: 0 8rpx 20rpx rgba(118, 224, 217, 0.3);

        &::after {
            display: none;
        }
    }
}

.tip-container {
    display: flex;
    align-items: center;
    margin-top: 10rpx;
    margin-left: 75%;
}

.hint-text {
    font-size: 22rpx;
    color: #a8acb2;
    display: flex;
    align-items: center;
    right: 0rpx;
}

.search-box {
    padding: 30rpx 40rpx 20rpx;
    background: #fff;

    /deep/ .u-search {
        background: #f8f8f8;
        border-radius: 50rpx;
        padding: 0 30rpx;

        .u-search__content {
            background: transparent;
        }
    }
}

/* 设备操作区域样式 */
.device-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx 40rpx;
    background: #f8f8f8;
    border-bottom: 1rpx solid #e5e5e5;
}

.device-count {
    font-size: 24rpx;
    color: #666;
}

/* 设备项样式 */
.device-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx 40rpx;
    border-bottom: 1rpx solid #f0f0f0;
    transition: all 0.2s;

    &:last-child {
        border-bottom: none;
    }
}

.device-info {
    flex: 1;
    min-width: 0;
}

.device-name {
    font-size: 30rpx;
    color: #333;
    font-weight: 500;
    margin-bottom: 8rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.device-details {
    display: flex;
    align-items: center;
    gap: 20rpx;
}

.device-id {
    font-size: 22rpx;
    color: #999;
    font-family: monospace;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 200rpx;
}

.signal-strength {
    font-size: 20rpx;
    color: #666;
    background: #f0f0f0;
    padding: 4rpx 8rpx;
    border-radius: 8rpx;
}

.device-status {
    flex-shrink: 0;
    margin-left: 20rpx;
}

.status-connecting {
    color: #ff9500;
    font-size: 24rpx;
    font-weight: 500;
}

.status-connected {
    color: #34c759;
    font-size: 24rpx;
    font-weight: 500;
}

.status-available {
    color: #8e8e93;
    font-size: 24rpx;
}

/deep/ .u-action-sheet {
    border-radius: 40rpx 40rpx 0 0;
    overflow: hidden;

    .u-action-sheet__header {
        font-weight: 500;
        padding: 40rpx 0 30rpx;
        color: #333;
    }
}
</style>