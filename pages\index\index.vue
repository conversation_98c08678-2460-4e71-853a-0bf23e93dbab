<template>
	<view class="bg-white">
		<view class="bg">
			<image class="bg-img" src="@/static/logo.png"></image>
		</view>
		<view class="form">
			<u--form labelPosition="left" :model="model1" :rules="rules" ref="uForm" :labelWidth="200">
				<u-form-item label="检查项目" prop="userInfo.checkTypeName" borderBottom @click="checkTypeShow = true;">
					<u--input v-model="model1.userInfo.checkTypeName" readonly disabledColor="#ffffff"
						placeholder="请选检查项目" border="none" clearable></u--input>
					<u-icon slot="right" name="arrow-right"></u-icon>
				</u-form-item>
				<u-form-item label="学校" prop="userInfo.schoolName" borderBottom @click="openSchoolShow">
					<u--input v-model="model1.userInfo.schoolName" readonly disabledColor="#ffffff" placeholder="请选择学校"
						border="none" clearable></u--input>
					<u-icon slot="right" name="arrow-right"></u-icon>
				</u-form-item>
				<u-form-item label="设备" prop="userInfo.deviceName" borderBottom @click="openDeviceShow">
					<u--input v-model="model1.userInfo.deviceName" readonly disabledColor="#ffffff" placeholder="请选择设备"
						border="none" clearable></u--input>
					<u-icon slot="right" name="arrow-right"></u-icon>
				</u-form-item>
				<view class="startCheck">
					<u-button type="primary" size="small" text="扫描学生二维码开始检查" @click="startCheckCode"></u-button>
					<view class="tip-container">
						<view class="hint-text" @click="handleNoStudentTip">
							没有学生？
						</view>
					</view>
				</view>
			</u--form>

			<u-action-sheet :show="checkTypeShow" @close="closeCheckType" title="请输入检查项目">
				<scroll-view scroll-y class="action-sheet-list">
					<view v-for="(item, index) in checkTypeList" :key="index" class="action-sheet-item"
						@tap="checkTypeSelect(item)" hover-class="item-hover">
						{{ item.label }}
					</view>

					<view v-if="checkTypeList.length === 0" class="empty-tip">
						暂无匹配数据
					</view>
				</scroll-view>
			</u-action-sheet>

			<u-action-sheet :show="schoolShow" @close="closeSchool" title="请输入学校">
				<view class="search-box">
					<u-search v-model="schoolKeyword" placeholder="请选择学校" :showAction="false" @change="onSchoolSearch"
						:clearabled="true"></u-search>
				</view>
				<scroll-view scroll-y class="action-sheet-list">
					<view v-for="(item, index) in filteredSchoolList" :key="index" class="action-sheet-item"
						@tap="schoolSelect(item)" hover-class="item-hover">
						{{ item.name }}
					</view>

					<view v-if="filteredSchoolList.length === 0" class="empty-tip">
						暂无匹配数据
					</view>
				</scroll-view>
			</u-action-sheet>

			<u-action-sheet :show="deviceShow" @close="closeDevice" title="请输入设备">
				<view class="search-box">
					<u-search v-model="deviceKeyword" placeholder="请选择设备" :showAction="false" @change="onDeviceSearch"
						:clearabled="true"></u-search>
				</view>
				<scroll-view scroll-y class="action-sheet-list">
					<view v-for="(item, index) in filteredDeviceList" :key="index" class="action-sheet-item"
						@tap="deviceSelect(item)" hover-class="item-hover">
						{{ item.name }}
						<text v-if="item.isConnected" style="color:green">（✓ 已连接）</text>
						<text v-else style="color:gray">（可连接）</text>
					</view>

					<view v-if="filteredDeviceList.length === 0" class="empty-tip">
						暂无匹配数据
					</view>
				</scroll-view>
			</u-action-sheet>
		</view>
	</view>
</template>

<script>
	import {
		getSchoolList,
	} from '@/utils/api/school.js'
	import {
		getStudentById,
	} from '@/utils/api/student.js'

	export default {
		components: {

		},
		data() {
			return {
				checkTypeShow: false,
				checkTypeList: [{
					label: '视力',
					id: '1'
				}, {
					label: '屈光',
					id: '2'
				}],
				isAndroid: false, // 新增平台标识
				model1: {
					userInfo: {
						checkType: '',
						checkTypeName: '',
						schoolName: '',
						schoolId: '',
					},
				},
				rules: {

				},
				schoolList: [],
				schoolShow: false,
				schoolKeyword: '',
				deviceList: [],
				deviceShow: false,
				deviceKeyword: '',
				jsonStr: '',
				_retryCount: 0,
				_reconnectCount: 0,
			}
		},
		beforeDestroy() {
			clearInterval(this.heartbeatTimer);
			clearTimeout(this.reconnectTimer);
		},
		computed: {
			filteredSchoolList() {
				if (!this.schoolKeyword) {
					return this.schoolList;
				}
				const keyword = this.schoolKeyword.toLowerCase();
				return this.schoolList.filter(item =>
					item.name.toLowerCase().includes(keyword)
				);
			},

			filteredDeviceList() {
				if (!this.deviceKeyword) {
					return this.deviceList;
				}
				const keyword = this.deviceKeyword.toLowerCase();
				return this.deviceList.filter(item =>
					item.name.toLowerCase().includes(keyword)
				);
			},
		},
		watch: {

		},
		onLoad(e) {

		},
		onShow() {
			// 每次显示页面时检查蓝牙状态
			this.checkBluetoothAdapterState();
		},
		onUnload() {
			if (this.listener) {
				uni.offBluetoothDeviceFound(this.listener);
			}
		},
		methods: {

			// 检查蓝牙适配器状态
			checkBluetoothAdapterState() {
				uni.getBluetoothAdapterState({
					success: (res) => {
						if (!res.available) {
							this.showToast('蓝牙不可用，请检查设备');
						}
					},
					fail: (err) => {
						console.error('蓝牙状态获取失败:', err);
						this.showToast('蓝牙状态获取失败');
					}
				});
			},
			closeCheckType() {
				this.checkTypeShow = false
			},
			closeSchool() {
				this.schoolKeyword = ''
				this.schoolShow = false
			},
			closeDevice() {
				this.deviceKeyword = ''
				this.deviceShow = false
			},
			checkTypeSelect(e) {
				this.model1.userInfo.checkTypeName = e.label
				this.model1.userInfo.checkType = e.id
				this.closeCheckType()
			},
			schoolSelect(e) {
				this.model1.userInfo.schoolName = e.name
				this.model1.userInfo.schoolId = e.id
				this.closeSchool()
			},
			async deviceSelect(e) {
				const _this = this;

				// 增强UUID配置（兼容主流厂商）
				const UUID_PROFILES = {
					STANDARD_SPP: {
						service: '00001101-0000-1000-8000-00805F9B34FB',
						writeChar: '00001101-0000-1000-8000-00805F9B34FB'
					},
					WCH_CH9140: {
						service: '0000FFE0-0000-1000-8000-00805F9B34FB',
						writeChar: '0000FFE1-0000-1000-8000-00805F9B34FB'
					},
					HC_05: {
						service: '00001101-0000-1000-8000-00805F9B34FB',
						writeChar: '00001102-0000-1000-8000-00805F9B34FB'
					}
				};

				uni.showLoading({
					title: '连接中...',
					mask: true
				});

				// 超时控制（25秒）
				const timeout = setTimeout(() => {
					uni.hideLoading();
					uni.$u.toast('连接超时');
					_this._resetConnection();
				}, 15000);

				try {
					// 增强连接协议
					await _this._createConnection(e.deviceId);
					await new Promise(resolve => setTimeout(resolve, 1500));

					// 服务发现流程优化
					const services = await _this._getBLEServices(e.deviceId);
					let sppProfile = null;

					// 多协议服务匹配
					for (const profile of Object.values(UUID_PROFILES)) {
						const service = services.find(s =>
							s.uuid.toUpperCase() === profile.service.toUpperCase()
						);
						if (service) {
							sppProfile = {
								...profile,
								serviceId: service.uuid
							};
							break;
						}
					}

					if (!sppProfile) throw new Error('未找到兼容的SPP服务');

					// 特征值发现优化
					const characteristics = await _this._getCharacteristics(
						e.deviceId,
						sppProfile.serviceId
					);

					const writeChar = characteristics.find(c =>
						c.uuid.toUpperCase() === sppProfile.writeChar.toUpperCase() &&
						(c.properties.write || c.properties.writeWithoutResponse)
					);

					if (!writeChar) throw new Error('写入特征不可用');

					// 设备参数增强
					_this._deviceParams = {
						deviceId: e.deviceId,
						name: e.name,
						serviceId: sppProfile.serviceId,
						writeCharId: writeChar.uuid,
						characteristics: characteristics,
						mtu: 20, // 初始默认值
						protocol: sppProfile.type || 'UNKNOWN',
						manufacturer: e.manufacturerData ?
							this._parseManufacturerData(e.manufacturerData) : 'Unknown'
					};

					// Android MTU协商
					// #ifdef APP-PLUS
					const mtuRes = await new Promise(resolve => {
						plus.bluetooth.setBLEMTU({
							deviceId: e.deviceId,
							mtu: 512,
							success: resolve,
							fail: () => resolve({
								mtu: 23
							})
						});
					});
					_this._deviceParams.mtu = mtuRes.mtu;
					// #endif

					// 发送设备初始化指令
					await _this._sendInitSequence();

					// 状态更新
					clearTimeout(timeout);
					uni.hideLoading();
					uni.$u.toast(`${e.name}连接成功`);
					this.model1.userInfo.deviceName = e.name
					_this._updateDeviceInfo(e);

					// 启动数据通道
					await _this._startNotification();

				} catch (error) {
					clearTimeout(timeout);
					uni.hideLoading();
					uni.$u.toast(`连接失败: ${error.message}`);
					_this._resetConnection();
				}
			},

			// 增强版特征值获取方法（支持错误重试机制）
			async _getCharacteristics(deviceId, serviceId) {
				try {
					const {
						characteristics
					} = await new Promise((resolve, reject) => {
						uni.getBLEDeviceCharacteristics({
							deviceId,
							serviceId,
							success: (res) => {
								if (res.errMsg !== 'getBLEDeviceCharacteristics:ok') {
									return reject(new Error('特征值获取失败'));
								}
								// 特征值有效性验证
								if (!res.characteristics || res.characteristics.length === 0) {
									return reject(new Error('未发现可用特征值'));
								}
								resolve(res);
							},
							fail: (err) => reject(err)
						});
					});

					// 调试日志输出（生产环境需关闭）
					console.log('[BLE Characteristics]',
						characteristics.map(c => ({
							uuid: c.uuid,
							properties: c.properties
						}))
					);

					return characteristics;
				} catch (error) {
					// 自动重试机制（最多3次）
					if (this._retryCount < 3) {
						this._retryCount++;
						await new Promise(resolve => setTimeout(resolve, 500));
						return this._getCharacteristics(deviceId, serviceId);
					}
					throw new Error(`特征值获取失败: ${error.message}`);
				}
			},

			decode(buffer) {
				const bytes = new Uint8Array(buffer);
				let str = '';

				for (let i = 0; i < bytes.length;) {
					const byte1 = bytes[i++];

					if (byte1 < 0x80) {
						str += String.fromCharCode(byte1);
					} else if (byte1 >= 0xC0 && byte1 < 0xE0 && i < bytes.length) {
						const byte2 = bytes[i++];
						str += String.fromCharCode(
							((byte1 & 0x1F) << 6) | (byte2 & 0x3F)
						);
					} else if (byte1 >= 0xE0 && byte1 < 0xF0 && i + 1 < bytes.length) {
						const byte2 = bytes[i++];
						const byte3 = bytes[i++];
						str += String.fromCharCode(
							((byte1 & 0x0F) << 12) |
							((byte2 & 0x3F) << 6) |
							(byte3 & 0x3F)
						);
					} else {
						str += '�'; // 替换无法解码的字符
					}
				}
				return str;
			},

			_processRS232Data(buffer) {
				const str = this.decode(buffer)
				if (!str.includes('}')) {
					this.jsonStr += str
				} else {
					this.jsonStr += str
					this.$store.dispatch('updateDiopterData', JSON.parse(this.jsonStr));
					this.jsonStr = ''
				}
			},

			_reconnect() {
				if (this._reconnectCount < 3) {
					this._reconnectCount++;
					setTimeout(async () => {
						try {
							await this.deviceSelect({
								deviceId: this._deviceParams.deviceId,
								name: this._deviceParams.name
							});
						} catch (e) {
							this._reconnect();
						}
					}, 2000);
				}
			},

			_updateDeviceInfo(device) {
				this._deviceParams.lastConnected = new Date().toISOString();
				this._deviceParams.signalStrength = device.RSSI;

				uni.setStorageSync('btDevice', JSON.stringify({
					...this._deviceParams,
					secure: this._isSecureConnection()
				}));

				this.deviceList = this.deviceList.map(item => ({
					...item,
					isConnected: item.deviceId === device.deviceId,
					connectionStatus: item.deviceId === device.deviceId ?
						'connected' : 'disconnected'
				}));
			},

			_isSecureConnection() {
				const deviceInfo = uni.getConnectedBluetoothDevices()[0] || {}
				const isBonded = deviceInfo.bonded || false // Android配对状态

				const characteristics = this._deviceParams?.characteristics || []
				const hasEncryption = characteristics.some(c => {
					console.log('[Security Check] Characteristic:', {
						uuid: c.uuid,
						properties: c.properties
					})
					return c.properties.encryptable || c.properties.authenticated
				})

				const crcValid = true // 临时跳过CRC验证

				return isBonded && hasEncryption && crcValid
			},

			async _sendInitSequence() {
				// 发送流控制指令（RTS/CTS）
				const flowControlCmd = new Uint8Array([0x41, 0x54, 0x2B, 0x46, 0x4C, 0x4F, 0x57, 0x3D, 0x31, 0x0D,
					0x0A
				]); // AT+FLOW=1
				await this._writeBLEValue(flowControlCmd);

				// 设置波特率（示例：115200）
				const baudRateCmd = new Uint8Array([0x41, 0x54, 0x2B, 0x42, 0x41, 0x55, 0x44, 0x3D, 0x31, 0x31, 0x35,
					0x32, 0x30, 0x30, 0x0D, 0x0A
				]); // AT+BAUD=115200
				await this._writeBLEValue(baudRateCmd);

				await new Promise(resolve => setTimeout(resolve, 300));
			},

			async _writeBLEValue(buffer) {
				return new Promise((resolve, reject) => {
					uni.writeBLECharacteristicValue({
						deviceId: this._deviceParams.deviceId,
						serviceId: this._deviceParams.serviceId,
						characteristicId: this._deviceParams.writeCharId,
						value: buffer.buffer ? buffer.buffer : new Uint8Array(buffer).buffer,
						success: resolve,
						fail: reject
					});
				});
			},

			_createConnection(deviceId) {
				return new Promise((resolve, reject) => {
					// 尝试BLE连接
					uni.createBLEConnection({
						deviceId,
						success: resolve,
						fail: (err) => {
							// 若失败尝试经典蓝牙连接
							uni.connectSocket({
								url: `bluetooth://${deviceId}?protocol=rfcomm&channel=1`,
								protocols: ['binary'],
								success: resolve,
								fail: reject
							});
						}
					});
				});
			},

			// 辅助方法：获取蓝牙服务
			_getBLEServices(deviceId) {
				return new Promise((resolve, reject) => {
					uni.getBLEDeviceServices({
						deviceId,
						success: (res) => resolve(res.services),
						fail: (err) => reject(err)
					});
				});
			},

			// 辅助方法：获取特征值
			_getBLECharacteristics(deviceId, serviceId) {
				return new Promise((resolve, reject) => {
					uni.getBLEDeviceCharacteristics({
						deviceId,
						serviceId,
						success: (res) => resolve(res.characteristics),
						fail: (err) => reject(err)
					});
				});
			},

			_resetCache() {
				this.bleState.chunkCache.forEach((_, key) => this.bleState.chunkCache.delete(key));
				this.bleState.currentBuffer = [];
			},

			_startNotification() {
				uni.notifyBLECharacteristicValueChange({
					deviceId: this._deviceParams.deviceId,
					serviceId: this._deviceParams.serviceId,
					characteristicId: this._deviceParams.writeCharId,
					state: true,
					success: () => {
						uni.onBLECharacteristicValueChange(res => {
							if (res.deviceId === this._deviceParams.deviceId &&
								res.characteristicId === this._deviceParams.writeCharId) {
								this._processRS232Data(res.value);
							}
						});

						this._heartbeatTimer = setInterval(() => {
							this._sendHeartbeat();
						}, 5000);
					},
					fail: (err) => {
						uni.$u.toast('数据通道初始化失败');
						this._reconnect(); // 自动重连机制
					}
				});

				// #ifdef APP-PLUS
				plus.bluetooth.setBLEMTU({
					deviceId: this._deviceParams.deviceId,
					mtu: 251,
					fail: () => console.warn('MTU协商失败')
				});
				// #endif
			},

			_sendHeartbeat() {
				const heartbeatData = new Uint8Array([0x01, 0xFE])
				this._writeBLEValue(heartbeatData).catch(err => {
					this._reconnect()
				})
			},

			async openSchoolShow() {
				const res = await getSchoolList()
				this.schoolList = res.data.data
				this.schoolShow = true
			},

			// 新增：统一权限检查方法
			async checkBluetoothPermissions() {
				try {
					const platform = uni.getSystemInfoSync().platform
					const isAndroid = platform.toLowerCase() === 'android'

					// Android必须获取定位权限
					if (this.isAndroid) {
						const locationSetting = await this.getAuthSetting('scope.userLocation')
						if (!locationSetting) {
							const result = await this.requestLocationPermission();
							if (!result) return false; // 用户拒绝了定位权限
						}
					}

					// 所有平台都需要蓝牙权限
					const bluetoothGranted = await this.checkPermissionStatus('scope.bluetooth');
					if (!bluetoothGranted) {
						const result = await this.requestBluetoothPermission();
						if (!result) return false; // 用户拒绝了蓝牙权限
					}

					return true
				} catch (error) {
					console.error('权限检查失败:', error)
					return false
				}
			},

			// 检查权限状态
			checkPermissionStatus(scope) {
				return new Promise(resolve => {
					uni.getSetting({
						success: (res) => {
							// 处理微信小程序的权限键名格式
							const key = scope.startsWith('scope.') ? scope.substring(6) : scope;
							resolve(!!res.authSetting[key]);
						},
						fail: () => resolve(false)
					});
				});
			},

			// 新增：获取权限设置状态
			getAuthSetting(scope) {
				return new Promise((resolve) => {
					uni.getSetting({
						success: (res) => {
							resolve(res.authSetting[scope] || false)
						},
						fail: () => resolve(false)
					})
				})
			},

			// 新增：请求定位权限
			requestLocationPermission() {
				return new Promise((resolve) => {
					uni.authorize({
						scope: 'scope.userLocation',
						success: () => resolve(true),
						fail: (err) => {
							console.error('定位权限请求失败:', err);
							this.showPermissionGuide('定位', '用于发现蓝牙设备');
							resolve(false);
						}
					});
				});
			},

			// 新增：请求蓝牙权限
			requestBluetoothPermission() {
				return new Promise((resolve) => {
					uni.authorize({
						scope: 'scope.bluetooth',
						success: () => resolve(true),
						fail: (err) => {
							console.error('蓝牙权限请求失败:', err);
							this.showPermissionGuide('蓝牙', '用于连接检测设备');
							resolve(false);
						}
					});
				});
			},

			// 新增：权限引导弹窗
			showPermissionGuide(permissionName, usage) {
				uni.showModal({
					title: `${permissionName}权限未开启`,
					content: `请在小程序设置中开启${permissionName}权限${usage ? '，' + usage : ''}`,
					confirmText: '立即开启',
					cancelText: '稍后再说',
					success: (res) => {
						if (res.confirm) {
							// 直接打开小程序设置页
							uni.openSetting({
								success: (settingRes) => {
									console.log('设置页打开成功', settingRes);
								},
								fail: (err) => {
									console.error('打开设置页失败:', err);
									this.showToast('打开设置失败，请手动前往小程序设置开启权限');
								}
							});
						}
					}
				});
			},


			async openDeviceShow() {
				try {

					// 步骤1：检查权限
					const hasPermissions = await this.checkBluetoothPermissions()
					if (!hasPermissions) {
						uni.showModal({
							title: '无法使用蓝牙',
							content: '您拒绝了蓝牙权限，将无法使用设备扫描功能',
							confirmText: '重新授权',
							success: (res) => {
								if (res.confirm) this.openDeviceShow(); // 递归重试
							}
						});
						return;
					}

					// 步骤2：初始化蓝牙适配器
					await this.initBluetoothAdapter()
				} catch (error) {
					this.handleBluetoothInitError(error)
					return
				}

				const _this = this;
				// 标准SPP服务UUID（蓝牙串口协议）
				const SPP_SERVICE_UUID = '00001101-0000-1000-8000-00805F9B34FB';
				// 写特征值（根据设备文档可能需要调整）
				const WRITE_CHARACTERISTIC_UUID = '0000ffe1-0000-1000-8000-00805f9b34fb';

				uni.openBluetoothAdapter({
					success: function() {
						uni.authorize({
							scope: 'scope.bluetooth',
							success: function() {
								uni.getBluetoothAdapterState({
									success: function(res) {
										if (!res.available) {
											uni.$u.toast('蓝牙不可用');
										} else {
											uni.startBluetoothDevicesDiscovery({
												allowDuplicatesKey: false,
												interval: 200,
												powerLevel: 'medium',
												success: () => {
													uni.showLoading({
														title: '搜索设备中...',
														mask: true
													});

													// 设备过滤条件优化
													const
														listener =
														uni
														.onBluetoothDeviceFound(
															devices => {
																const
																	validDevices =
																	devices
																	.devices
																	.map(
																		item =>
																		({
																			...
																			item,
																			isConnected: item
																				.connected ||
																				false
																		})
																	)
																	.filter(
																		item =>
																		item
																		.connectable &&
																		(item
																			.name
																			.includes(
																				'RS232'
																			) ||
																			/^([0-9A-F]{2}:){5}[0-9A-F]{2}$/
																			.test(
																				item
																				.deviceId
																			)
																		) &&
																		item
																		.name &&
																		item
																		.advertisServiceUUIDs
																		.length >
																		0
																	);

																const
																	newDevices =
																	validDevices
																	.filter(
																		newDevice =>
																		!
																		_this
																		.deviceList
																		.some(
																			existDevice =>
																			existDevice
																			.deviceId ===
																			newDevice
																			.deviceId
																		)
																	);

																if (newDevices
																	.length >
																	0
																) {
																	_this
																		.deviceList = [
																			...
																			_this
																			.deviceList,
																			...
																			newDevices
																		];
																	_this
																		.deviceShow =
																		true;
																}
															});

													// 增强型设备连接方法
													function connectDevice(
														deviceId) {
														uni.createBLEConnection({
															deviceId,
															success: () => {
																// 获取服务列表
																uni.getBLEDeviceServices({
																	deviceId,
																	success: (
																		servicesRes
																	) => {
																		const
																			sppService =
																			servicesRes
																			.services
																			.find(
																				s =>
																				s
																				.uuid
																				.toUpperCase() ===
																				SPP_SERVICE_UUID
																				.toUpperCase()
																			);

																		if (
																			sppService
																		) {
																			// 获取特征值
																			uni.getBLEDeviceCharacteristics({
																				deviceId,
																				serviceId: sppService
																					.uuid,
																				success: (
																					charsRes
																				) => {
																					const
																						writeChar =
																						charsRes
																						.characteristics
																						.find(
																							c =>
																							c
																							.uuid
																							.toUpperCase() ===
																							WRITE_CHARACTERISTIC_UUID
																							.toUpperCase() &&
																							c
																							.properties
																							.write
																						);

																					if (
																						writeChar
																					) {
																						// 存储必要参数
																						_this
																							._deviceId =
																							deviceId;
																						_this
																							._serviceId =
																							sppService
																							.uuid;
																						_this
																							._writeCharId =
																							writeChar
																							.uuid;

																						// 更新设备状态
																						_this
																							.deviceList =
																							_this
																							.deviceList
																							.map(
																								item =>
																								item
																								.deviceId ===
																								deviceId ?
																								{
																									...
																									item,
																									isConnected: true
																								} :
																								item
																							);

																						uni.$u
																							.toast(
																								'连接成功'
																							);
																						this.closeDevice()
																					} else {
																						uni.$u
																							.toast(
																								'未找到写入特征'
																							);
																					}
																				},
																				fail: () =>
																					uni
																					.$u
																					.toast(
																						'特征值获取失败'
																					)
																			});
																		} else {
																			uni.$u
																				.toast(
																					'未找到SPP服务'
																				);
																		}
																	},
																	fail: () =>
																		uni
																		.$u
																		.toast(
																			'服务获取失败'
																		)
																});
															},
															fail: () =>
																uni
																.$u
																.toast(
																	'连接失败'
																)
														});
													}

													// 连接状态监听
													uni.onBLEConnectionStateChange(
														res => {
															_this
																.deviceList =
																_this
																.deviceList
																.map(
																	item =>
																	item
																	.deviceId ===
																	res
																	.deviceId ?
																	{
																		...
																		item,
																		isConnected: res
																			.connected
																	} :
																	item
																);

															if (!
																res
																.connected
															) {
																_this
																	._deviceId =
																	null;
																_this
																	._serviceId =
																	null;
																_this
																	._writeCharId =
																	null;
															}
														});

													// 10秒后停止搜索
													setTimeout(
														() => {
															uni
																.hideLoading();
															uni
																.stopBluetoothDevicesDiscovery();
															uni.offBluetoothDeviceFound(
																listener
															);
														},
														10000);
												}
											});
										}
									},
									fail: () => uni.$u.toast('获取蓝牙状态失败')
								});
							},
							fail: () => uni.$u.toast('请先授权蓝牙权限')
						});
					},
					fail: (err) => {
						if (err.errCode === 10001) {
							uni.$u.toast('请开启手机定位功能');
						} else {
							uni.$u.toast('请先开启蓝牙');
						}
					}
				});
			},

			// 新增：蓝牙适配器初始化
			initBluetoothAdapter() {
				return new Promise((resolve, reject) => {
					uni.openBluetoothAdapter({
						success: resolve,
						fail: (err) => {
							console.error('蓝牙适配器初始化失败:', err)
							// 增加特定错误代码处理
							if (err.errCode === 10001) {
								uni.showToast({
									title: '请开启手机蓝牙',
									icon: 'none'
								})
							} else if (err.errCode === 10004) {
								uni.showToast({
									title: '不支持蓝牙功能',
									icon: 'none'
								})
							}
							reject(err)
						}
					})
				})
			},

			// 新增：蓝牙初始化错误处理
			handleBluetoothInitError(err) {
				console.error('蓝牙初始化失败:', err)

				// 根据错误码提供精准引导
				switch (err.errCode) {
					case 10001: // 蓝牙未开启
						uni.showModal({
							title: '蓝牙未开启',
							content: '请开启手机蓝牙功能',
							confirmText: '去开启',
							success: ({
								confirm
							}) => {
								if (confirm) uni.openBluetoothAdapter()
							}
						})
						break
					case 10002: // 定位未开启（仅Android）
						if (this.isAndroid) {
							uni.showModal({
								title: '定位服务未开启',
								content: '安卓系统需要开启定位服务才能搜索蓝牙设备',
								confirmText: '去开启',
								success: ({
									confirm
								}) => {
									if (confirm) uni.openLocationSetting()
								}
							})
						}
						break
					default:
						uni.showToast({
							title: `蓝牙初始化失败[${err.errCode}]`,
							icon: 'none'
						})
				}
			},

			onSchoolSearch(value) {
				this.schoolKeyword = value;
			},
			onDeviceSearch(value) {
				this.deviceKeyword = value;
			},
			startCheckCode() {
				if (!this.model1.userInfo.checkType) {
					uni.$u.toast('请先选择检查项目');
					return
				}
				if (!this.model1.userInfo.schoolId) {
					uni.$u.toast('请先选择学校');
					return
				}
				if (!this.model1.userInfo.deviceName && this.model1.userInfo.checkType == 2) {
					uni.$u.toast('请先选择设备');
					return
				}
				if (this.model1.userInfo.checkType == 1) {
					uni.scanCode({
						success: (res) => {
							getStudentById(res.result).then(response => {
								if (response.data.data) {
									if(response.data.data.schoolId != this.model1.userInfo.schoolId){
										uni.$u.toast('学生与学校不匹配');
										return
									}
									uni.navigateTo({
										url: "/pageA/scCheck/visionFormIndex?psnId=" +
											response
											.data.data.id + '&psnName=' + response
											.data.data
											.name
									})
								} else {
									uni.$u.toast('请扫描正确的学生二维码');
								}
							})

						},
						fail: (err) => {
							console.log('扫一扫失败：', err);
						}
					});
				}
				if (this.model1.userInfo.checkType == 2) {
					uni.scanCode({
						success: (res) => {
							getStudentById(res.result).then(response => {
								if (response.data.data) {
									uni.navigateTo({
										url: "/pageA/scCheck/diopterFormIndex?psnId=" +
											response
											.data.data.id + '&psnName=' + response
											.data.data
											.name
									})
								} else {
									uni.$u.toast('请扫描正确的学生二维码');
								}
							})

						},
						fail: (err) => {
							console.log('扫一扫失败：', err);
						}
					});
					return;
				}
			},
		}
	}
</script>

<style lang="less" scoped>
	.bg {
		width: 100%;
		background: linear-gradient(180deg, #e3f3f0 0%, #f8fcff 100%);
		position: relative;
		overflow: hidden;

		&::after {
			content: '';
			position: absolute;
			bottom: -20rpx;
			left: 0;
			width: 100%;
			height: 40rpx;
			background: #ffffff;
			border-radius: 40rpx 40rpx 0 0;
		}
	}

	.bg-img {
		width: 100%;
		height: 360rpx;
		transform: scale(1.2);
		opacity: 0.9;
	}

	.form {
		margin-top: 40rpx;
		padding: 40rpx;
		background: #ffffff;
		border-radius: 40rpx 40rpx 0 0;
		box-shadow: 0 -10rpx 30rpx rgba(58, 119, 113, 0.05);

		/deep/ .u-form-item {
			padding: 28rpx 0;
			margin: 10rpx 0;
			border-radius: 12rpx;
			transition: all 0.3s;

			&:active {
				background: #f8f8f8;
			}

			.u-form-item__body {
				padding: 0 20rpx;
			}

			.u-icon {
				color: #999;
				margin-left: 20rpx;
			}
		}
	}

	.action-sheet-list {
		max-height: 60vh;
		padding: 0 30rpx;
	}

	.action-sheet-item {
		padding: 32rpx 40rpx;
		font-size: 30rpx;
		color: #333;
		border-bottom: 1rpx solid #f0f0f0;
		transition: all 0.2s;
		position: relative;

		&::after {
			content: '';
			position: absolute;
			left: 40rpx;
			right: 40rpx;
			bottom: 0;
			height: 1rpx;
			background: #f5f5f5;
		}

		&:last-child::after {
			display: none;
		}
	}

	.item-hover {
		background: #f8f8f8;
		transform: translateX(10rpx);
	}

	.empty-tip {
		text-align: center;
		padding: 60rpx 0;
		color: #ccc;
		font-size: 28rpx;
	}

	.startCheck {
		margin-top: 80rpx;
		padding: 0 20rpx;
		position: relative;

		/deep/ .u-button {
			height: 88rpx;
			border-radius: 16rpx;
			font-size: 32rpx;
			letter-spacing: 2rpx;
			background: linear-gradient(45deg, #4dccc6, #76e0d9);
			box-shadow: 0 8rpx 20rpx rgba(118, 224, 217, 0.3);

			&::after {
				display: none;
			}
		}
	}

	.tip-container {
		display: flex;
		align-items: center;
		margin-top: 10rpx;
		margin-left: 75%;
	}

	.hint-text {
		font-size: 22rpx;
		color: #a8acb2;
		display: flex;
		align-items: center;
		right: 0rpx;
	}

	.search-box {
		padding: 30rpx 40rpx 20rpx;
		background: #fff;

		/deep/ .u-search {
			background: #f8f8f8;
			border-radius: 50rpx;
			padding: 0 30rpx;

			.u-search__content {
				background: transparent;
			}
		}
	}

	/deep/ .u-action-sheet {
		border-radius: 40rpx 40rpx 0 0;
		overflow: hidden;

		.u-action-sheet__header {
			font-weight: 500;
			padding: 40rpx 0 30rpx;
			color: #333;
		}
	}
</style>