const app = getApp()
var bleData = '';
var bleReposeData = '';

function inArray(arr, key, val) {
  for (let i = 0; i < arr.length; i++) {
    if (arr[i][key] === val) {
      return i;
    }
  }
  return -1;
}
function array2String(buffer) {
  let hexArr = Array.prototype.map.call(
    new Uint8Array(buffer),
    function (bit) {
      return ('00' + bit.toString(16)).slice(-2)
    }
  )
  return `${hexArr[7]}:${hexArr[6]}:${hexArr[5]}:${hexArr[2]}:${hexArr[1]}:${hexArr[0]}`
}
// ArrayBuffer转16进度字符串示例
function ab2hex(buffer) {
  var hexArr = Array.prototype.map.call(
    new Uint8Array(buffer),
    function (bit) {
      return ('00' + bit.toString(16)).slice(-2)
    }
  )
  return hexArr.join('');
}

Page({
  data: {
    response:false,
    udpport:'',
    devices: [],
    connected: false,
    chs: [],
    sendDataTxt: 1,
    sendDataValue:'{"Name":"","Name.Base64":"546L5a2m5a2m","BirthDate":"1949-09-21","Sex":0,"CaseId":"123","DeviceStart":true,"Response":true}',
    deviceCyl: true,
    rPupil: '',
    lPupil: '',
    rGazeV: '',
    lGazeV: '',
    rGazeH: '',
    lGazeH: '',
    pd: '',
    rSE1: '',
    rDS1: '',
    rDC1: '',
    rAxis1: '',
    rSE2: '',
    rDS2: '',
    rDC2: '',
    rAxis2: '',
    lSE1: '',
    lDS1: '',
    lDC1: '',
    lAxis1: '',
    lSE2: '',
    lDS2: '',
    lDC2: '',
    lAxis2: '',
  },
onLoad(){


},

  openBluetoothAdapter() {
    wx.openBluetoothAdapter({
      success: (res) => {
        console.log('openBluetoothAdapter success', res)
        this.setData({
          devices: []
        })
        this.startBluetoothDevicesDiscovery()
      },
      fail: (res) => {
        if (res.errCode === 10001) {
          wx.onBluetoothAdapterStateChange(function (res) {
            console.log('onBluetoothAdapterStateChange', res)
            if (res.available) {
              this.startBluetoothDevicesDiscovery()
            }
          })
        }
      }
    })
  },
  getBluetoothAdapterState() {
    wx.getBluetoothAdapterState({
      success: (res) => {
        console.log('getBluetoothAdapterState', res)
        if (res.discovering) {
          this.onBluetoothDeviceFound()
        } else if (res.available) {
          this.startBluetoothDevicesDiscovery()
        }
      }
    })
  },
  startBluetoothDevicesDiscovery() {
    if (this._discoveryStarted) {
      return
    }
    this._discoveryStarted = true
    wx.startBluetoothDevicesDiscovery({
      allowDuplicatesKey: true,
      success: (res) => {
        console.log('startBluetoothDevicesDiscovery success', res)
        this.onBluetoothDeviceFound()
      },
    })
  },
  stopBluetoothDevicesDiscovery() {
    wx.stopBluetoothDevicesDiscovery()
  },
  onBluetoothDeviceFound() {
    wx.onBluetoothDeviceFound((res) => {
      res.devices.forEach(device => {
        if (!device.name && !device.localName) {
          return
        }
        const foundDevices = this.data.devices
        console.log(foundDevices);
        const idx = inArray(foundDevices, 'deviceId', device.deviceId)
        const data = {}
        if (idx === -1) {
          data[`devices[${foundDevices.length}]`] = device
        } else {
          data[`devices[${idx}]`] = device
        }
        this.setData(data)
      })
    })
  },
  createBLEConnection(e) {
    const ds = e.currentTarget.dataset
    const deviceId = ds.deviceId
    const name = ds.name
    wx.createBLEConnection({
      deviceId,
      success: (res) => {
        this.setData({
          connected: true,
          name,
          deviceId,
        })
        this.getBLEDeviceServices(deviceId)
      }
    })
    this.stopBluetoothDevicesDiscovery()
  },
  //关闭连接
  closeBLEConnection() {
    wx.closeBLEConnection({
      deviceId: this.data.deviceId
    })
    this.setData({
      connected: false,
      chs: [],
      canWrite: false,
    })
  },
  getBLEDeviceServices(deviceId) {
    wx.getBLEDeviceServices({
      deviceId,
      success: (res) => {
        for (let i = 0; i < res.services.length; i++) {
          if (res.services[i].isPrimary) {
            this.getBLEDeviceCharacteristics(deviceId, res.services[i].uuid)
            return
          }
        }
      }
    })
  },
  getBLEDeviceCharacteristics(deviceId, serviceId) {
    wx.getBLEDeviceCharacteristics({
      deviceId,
      serviceId,
      success: (res) => {
        console.log('getBLEDeviceCharacteristics success', res.characteristics)
        for (let i = 0; i < res.characteristics.length; i++) {
          let item = res.characteristics[i]
          if (item.properties.read) {
            wx.readBLECharacteristicValue({
              deviceId,
              serviceId,
              characteristicId: item.uuid,
            })
          }
          if (item.properties.write) {
            this.setData({
              canWrite: true
            })
            this._deviceId = deviceId
            this._serviceId = serviceId
            this._characteristicId = item.uuid
            this.writeBLECharacteristicValue()
          }
          if (item.properties.notify || item.properties.indicate) {
            wx.notifyBLECharacteristicValueChange({
              deviceId,
              serviceId,
              characteristicId: item.uuid,
              state: true,
            })
          }
        }
      },
      fail(res) {
        console.error('getBLEDeviceCharacteristics', res)
      }
    })
    // 操作之前先监听，保证第一时间获取数据
    wx.onBLECharacteristicValueChange((characteristic) => {
      const idx = inArray(this.data.chs, 'uuid', characteristic.characteristicId)
      const data = {}
      if (this.data.response){
            if (this.bleReposeData == undefined || this.bleReposeData == null || this.bleReposeData === '') {
                this.bleReposeData =this.hexToString(ab2hex(characteristic.value))
            }else{
                this.bleReposeData +=this.hexToString(ab2hex(characteristic.value))
            }
            if (this.bleReposeData.indexOf("{") !== -1 && this.bleReposeData.indexOf("}") !== -1){
                var jsonStr = this.bleReposeData
                jsonStr = jsonStr.replace(" ", "")
                if (typeof jsonStr != 'object') {
                  jsonStr = jsonStr.replace(/\ufeff/g, "") //重点
                }
                var jsonObj = JSON.parse(jsonStr)
                if (this.bleReposeData.indexOf("Msg")>=0)
                {
                    wx.showToast({
                        title: 'Msg：' + jsonObj.Msg,
                        icon: 'success',
                        duration: 2000
                    })
                }
                this.setData({
                    "response":false
                }
                )
                this.bleReposeData='';
            }
        } 
        else 
         {
        if (this.bleData == undefined || this.bleData == null || this.bleData === '') {
             this.bleData = this.hexToString(ab2hex(characteristic.value))
            } else {
             this.bleData += this.hexToString(ab2hex(characteristic.value))
            }
             this.BleDataCheck()      
        }    
    })
  },
  //校验Ble数据是否接收完成
  BleDataCheck() {
    if (this.bleData.indexOf("SW") !== -1 && this.bleData.indexOf("FE") !== -1) {
        console.log("SW_____FE")
        console.log(this.bleData)      
        var eyeType = this.bleData.substring(21, 22)
        console.log(eyeType)
        if (eyeType == '1') {
          this.setData({
            rPupil: this.bleData.substring(40, 43),
            rSE1: this.bleData.substring(43, 48),
            rDS1: this.bleData.substring(48, 53),
            rDC1: this.bleData.substring(53, 58),
            rAxis1: this.bleData.substring(58, 61),
            rSE2: this.bleData.substring(61, 66),
            rDS2: this.bleData.substring(66, 71),
            rDC2: this.bleData.substring(71, 76),
            rAxis2: this.bleData.substring(76, 79),
            pd: this.bleData.substring(79, 81),
            rGazeH: this.FormatGazeHV(this.bleData.substring(85, 88), false),
            rGazeV: this.FormatGazeHV(this.bleData.substring(88, 91)),
          })
        } else if(eyeType == '2'){
          this.setData({
            lPupil: this.bleData.substring(40, 43),
            lSE1: this.bleData.substring(43, 48),
            lDS1: this.bleData.substring(48, 53),
            lDC1: this.bleData.substring(53, 58),
            lAxis1: this.bleData.substring(58, 61), 
            lSE2: this.bleData.substring(61, 66),
            lDS2: this.bleData.substring(66, 71),
            lDC2: this.bleData.substring(71, 76),
            lAxis2: this.bleData.substring(76, 79),
            pd: this.bleData.substring(79, 81),
            lGazeH: this.FormatGazeHV(this.bleData.substring(85, 88), false),
            lGazeV: this.FormatGazeHV(this.bleData.substring(88, 91)),
          })
        }else{
          console.log("SWjson")
          //SWjson    包含FE
          var startIndex = this.bleData.indexOf("{")
          var endIndex = this.bleData.indexOf("FE")
          var jsonStr = this.bleData.substring(startIndex, endIndex)
          console.log(jsonStr)
          jsonStr = jsonStr.replace(" ", "")
          if (typeof jsonStr != 'object') {
            jsonStr = jsonStr.replace(/\ufeff/g, ""); //重点
          }
          var jsonObj = JSON.parse(jsonStr)
          var clinicResult = jsonObj.ClinicResult[0]
          this.setData({
            rPupil: clinicResult.RPupil,
            lPupil: clinicResult.LPupil,
            pd: clinicResult.PD,
            rGazeV: this.FormatGazeHV(clinicResult.RGazeV),
            lGazeV: this.FormatGazeHV(clinicResult.LGazeV),
            rGazeH: this.FormatGazeHV(clinicResult.RGazeH, false),
            lGazeH: this.FormatGazeHV(clinicResult.LGazeH, false),
            rSE1: clinicResult.RSE1,
            rDS1: clinicResult.RDS1,
            rDC1: clinicResult.RDC1,
            rAxis1: clinicResult.RAxis1,
            rSE2: clinicResult.RSE2,
            rDS2: clinicResult.RDS2,
            rDC2: clinicResult.RDC2,
            rAxis2: clinicResult.RAxis2,
            lSE1: clinicResult.LSE1,
            lDS1: clinicResult.LDS1,
            lDC1: clinicResult.LDC1,
            lAxis1: clinicResult.LAxis1,
            lSE2: clinicResult.LSE2,
            lDS2: clinicResult.LDS2,
            lDC2: clinicResult.LDC2,
            lAxis2: clinicResult.LAxis2,
          })
        }
      this.writeBLECharacteristicValue()
      this.bleData = ''
      return;
    } else if (this.bleData.indexOf("FE") === -1 && this.bleData.indexOf("{") !== -1 && this.bleData.indexOf("}") !== -1) {
      //纯Json    不包含FE
      console.log("json")
      var jsonStr = this.bleData
      jsonStr = jsonStr.replace(" ", "")
      if (typeof jsonStr != 'object') {
        jsonStr = jsonStr.replace(/\ufeff/g, ""); //重点
      }
      var jsonObj = JSON.parse(jsonStr)
      var clinicResult = jsonObj.ClinicResult[0]
      console.log(this.bleData)
      this.setData({
        rPupil: clinicResult.RPupil,
        lPupil: clinicResult.LPupil,
        pd: clinicResult.PD,
        rGazeV: this.FormatGazeHV(clinicResult.RGazeV),
        lGazeV: this.FormatGazeHV(clinicResult.LGazeV),
        rGazeH: this.FormatGazeHV(clinicResult.RGazeH, false),
        lGazeH: this.FormatGazeHV(clinicResult.LGazeH, false),
        rSE1: clinicResult.RSE1,
        rDS1: clinicResult.RDS1,
        rDC1: clinicResult.RDC1,
        rAxis1: clinicResult.RAxis1,
        rSE2: clinicResult.RSE2,
        rDS2: clinicResult.RDS2,
        rDC2: clinicResult.RDC2,
        rAxis2: clinicResult.RAxis2,
        lSE1: clinicResult.LSE1,
        lDS1: clinicResult.LDS1,
        lDC1: clinicResult.LDC1,
        lAxis1: clinicResult.LAxis1,
        lSE2: clinicResult.LSE2,
        lDS2: clinicResult.LDS2,
        lDC2: clinicResult.LDC2,
        lAxis2: clinicResult.LAxis2,
      })
      this.writeBLECharacteristicValue()
      this.bleData = ''
      return;
    } else {}
  },


  //处理方向
  FormatGazeHV(str, isGazeV = true) {
    if (str === undefined || str == null || str === '' || str === 'undefined') return '';
    if (str == 0) return str;
    if (isGazeV) {
      if (str < 0) {
        return "⬆" + Math.abs(str)
      } else {
        return "⬇" + Math.abs(str)
      }
    } else {
      if (str < 0) {
        return "➡" + Math.abs(str)
      } else {
        return "⬅" + Math.abs(str)
      }
    }

  },

  //将16进制转为 字符串
  hexToString(str) {
    var val = "",
      len = str.length / 2;
    for (var i = 0; i < len; i++) {
      val += String.fromCharCode(parseInt(str.substr(i * 2, 2), 16));
    }
    return this.utf8to16(val);
  },
  //处理中文乱码问题
  utf8to16(str) {
    var out, i, len, c;
    var char2, char3;
    out = "";
    len = str.length;
    i = 0;
    while (i < len) {
      c = str.charCodeAt(i++);
      switch (c >> 4) {
        case 0:
        case 1:
        case 2:
        case 3:
        case 4:
        case 5:
        case 6:
        case 7:
          out += str.charAt(i - 1);
          break;
        case 12:
        case 13:
          char2 = str.charCodeAt(i++);
          out += String.fromCharCode(((c & 0x1F) << 6) | (char2 & 0x3F));
          break;
        case 14:
          char2 = str.charCodeAt(i++);
          char3 = str.charCodeAt(i++);
          out += String.fromCharCode(((c & 0x0F) << 12) |
            ((char2 & 0x3F) << 6) |
            ((char3 & 0x3F) << 0));
          break;
      }
    }
    return out;
  },

  //字符串转arrayBuffrt
  strToArrayBuffer(str) {
    var array = new Uint8Array(str.length);
    for (var i = 0; i < str.length; i++) {
      array[i] = str.charCodeAt(i);
    }
    return array.buffer
  },

  //发送SUCC
  writeBLECharacteristicValue() {
    wx.writeBLECharacteristicValue({
      deviceId: this._deviceId,
      serviceId: this._serviceId,
      characteristicId: this._characteristicId,
      value: this.strToArrayBuffer('SUCC'),
    })
  },
  //关闭蓝牙
  closeBluetoothAdapter() {
    wx.closeBluetoothAdapter()
    this._discoveryStarted = false

    this.setData({
      connected: false,
      chs: [],
      canWrite: false,
    })

  },
  deviceCylChange() {
    if (this.data.deviceCyl) {
      this.setData({
        deviceCyl: false
      })
    } else {
      this.setData({
        deviceCyl: true
      })
    }
  },
    sendDataValueChange(e) {
    this.setData({
        sendDataValue: e.detail.value
      })
  },
  sendDataTxtChange() {
    if (this.data.sendDataTxt == 1) {
      this.setData({
        sendDataTxt: 2
      })
      return;
    }
    if (this.data.sendDataTxt == 2) {
      this.setData({
        sendDataTxt: 3
      })
      return;

    } 
    if (this.data.sendDataTxt == 3) {
      this.setData({
        sendDataTxt: 1
      })
      return;
    } 
  },
  clearData() {
    this.setData({
     response:false,
      rPupil: '',
      rSE1: '',
      rDS1:'',
      rDC1: '',
      rAxis1: '',
      rSE2: '',
      rDS2: '',
      rDC2: '',
      rAxis2:'',
      pd: '',
      rGazeH: '',
      rGazeV: '',
      lPupil: '',
      lSE1: '',
      lDS1:'',
      lDC1: '',
      lAxis1: '',
      lSE2: '',
      lDS2: '',
      lDC2: '',
      lAxis2:'',
      lGazeH: '',
      lGazeV: '',
    })
    this.bleData='';
    this.bleReposeData='';
  },
  sendData() {

    var jsonStr = this.data.sendDataValue
    jsonStr = jsonStr.replace(" ", "")
    if (typeof jsonStr != 'object') {
      jsonStr = jsonStr.replace(/\ufeff/g, "") //重点
    }
    var jsonObj = JSON.parse(jsonStr)
    console.log(this.data.sendDataValue.indexOf("Response"))

    if (this.data.sendDataValue.indexOf("Response")>=0 ){
        if (jsonObj.Response == true){
            this.setData({
                "response":true
                }
            )
        }else{
            this.setData({
                "response":false
                }
            )
        }
    }else{
        this.setData({
            "response":true
            }
        )
    }
    wx.writeBLECharacteristicValue({
      deviceId: this._deviceId,
      serviceId: this._serviceId,
      characteristicId: this._characteristicId,
      value: this.strToArrayBuffer(JSON.stringify(jsonObj)),
    })
  },
  /**
 * 分享给朋友
 * */
onShareAppMessage() {
  console.log('fenxiang')
}
})